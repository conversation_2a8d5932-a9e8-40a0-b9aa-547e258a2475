import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useLanguageSync } from '@/hooks/use-language-sync';

interface FaqItem {
  id: string;
  question: string;
  answer: string;
}

const FAQ = () => {
  const { t } = useTranslation();
  // 使用自定义hook确保语言同步
  useLanguageSync('FAQ');
  // 跟踪当前打开的FAQ项目
  const [openItem, setOpenItem] = useState<string | null>(null);

  const toggleItem = (id: string) => {
    setOpenItem(openItem === id ? null : id);
  };

  const faqs: FaqItem[] = [
    {
      id: 'why',
      question: t('faq.why.question'),
      answer: t('faq.why.answer')
    },
    {
      id: 'secure',
      question: t('faq.secure.question'),
      answer: t('faq.secure.answer')
    },
    {
      id: 'mobile',
      question: t('faq.mobile.question'),
      answer: t('faq.mobile.answer')
    },
    {
      id: 'languages',
      question: t('faq.languages.question'),
      answer: t('faq.languages.answer')
    }
  ];

  return (
    <section className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-xl font-bold mb-6">{t('faq.title')}</h2>

      <div className="space-y-4">
        {faqs.map((faq) => (
          <div key={faq.id} className="border border-gray-200 rounded-lg">
            <button 
              onClick={() => toggleItem(faq.id)}
              className="flex justify-between w-full px-4 py-3 text-left text-gray-900 bg-gray-50 hover:bg-gray-100 rounded-lg focus:outline-none focus-visible:ring focus-visible:ring-primary"
            >
              <span className="font-medium">{faq.question}</span>
              <span className="material-icons">
                {openItem === faq.id ? 'remove' : 'add'}
              </span>
            </button>
            {openItem === faq.id && (
              <div className="px-4 py-3 text-gray-700 bg-white">
                {faq.answer}
              </div>
            )}
          </div>
        ))}
      </div>
    </section>
  );
};

export default FAQ;
