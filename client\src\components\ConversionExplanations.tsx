import { useTranslation } from 'react-i18next';
import { useLanguageSync } from '@/hooks/use-language-sync';

interface ExplanationItem {
  key: string;
  title: string;
  description: string[];
  example: string;
}

const ConversionExplanations = () => {
  const { t } = useTranslation();
  // 使用自定义hook确保语言同步
  useLanguageSync('ConversionExplanations');

  const explanations: ExplanationItem[] = [
    {
      key: 'sentence',
      title: t('explanations.sentence.title'),
      description: [
        t('explanations.sentence.description1'),
        t('explanations.sentence.description2')
      ],
      example: t('explanations.sentence.example')
    },
    {
      key: 'lower',
      title: t('explanations.lower.title'),
      description: [
        t('explanations.lower.description')
      ],
      example: t('explanations.lower.example')
    },
    {
      key: 'upper',
      title: t('explanations.upper.title'),
      description: [
        t('explanations.upper.description')
      ],
      example: t('explanations.upper.example')
    },
    {
      key: 'capitalized',
      title: t('explanations.capitalized.title'),
      description: [
        t('explanations.capitalized.description')
      ],
      example: t('explanations.capitalized.example')
    },
    {
      key: 'alternating',
      title: t('explanations.alternating.title'),
      description: [
        t('explanations.alternating.description')
      ],
      example: t('explanations.alternating.example')
    },
    {
      key: 'title',
      title: t('explanations.title.title'),
      description: [
        t('explanations.title.description')
      ],
      example: t('explanations.title.example')
    }
  ];

  return (
    <section className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-xl font-bold mb-6">{t('explanations.sectionTitle')}</h2>

      <div className="grid md:grid-cols-2 gap-8">
        {explanations.map((item) => (
          <div key={item.key}>
            <h3 className="text-lg font-semibold mb-2 text-primary">{item.title}</h3>
            {item.description.map((paragraph, index) => (
              <p key={index} className="mb-3">{paragraph}</p>
            ))}
            <p className="text-gray-600 italic mb-3">{item.example}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ConversionExplanations;
