import { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface NavItem {
  key: string;
  label: string;
}

const Navigation = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('textCase');

  const navItems: NavItem[] = [];

  return (
    <nav className="bg-white border-b border-gray-200 mb-6">
      <div className="container mx-auto px-4 overflow-x-auto">
        <div className="flex space-x-1 py-2 whitespace-nowrap">
          {navItems.map((item) => (
            <button
              key={item.key}
              className={`px-2 md:px-4 py-2 text-sm md:text-base ${activeTab === item.key
                ? 'text-primary border-b-2 border-primary font-medium'
                : 'text-gray-600 hover:text-primary'}`}
              onClick={() => setActiveTab(item.key)}
            >
              {item.label}
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
