import i18next from 'i18next';
import { createI18nInstanceForSSG, getI18nInstanceForLanguage } from './i18n';

/**
 * 测试SSG环境下的i18n实例隔离
 * 在SSG阶段，每次请求应该创建全新的i18n实例，即使是相同语言
 */
describe('i18n 在SSG环境下的行为', () => {
  // 模拟SSG环境
  const originalWindow = global.window;
  beforeEach(() => {
    // 移除window对象模拟SSG环境
    // @ts-ignore
    delete global.window;
  });
  
  afterEach(() => {
    // 恢复window对象
    global.window = originalWindow;
  });
  
  it('在SSG环境下，每次请求应创建新的i18n实例，即使是相同语言', () => {
    // 创建两个相同语言的实例，应该是不同的实例
    const instance1 = createI18nInstanceForSSG('en');
    const instance2 = createI18nInstanceForSSG('en');
    
    expect(instance1).not.toBe(instance2);
  });
  
  it('使用getI18nInstanceForLanguage在SSG环境下应该总是创建新实例', () => {
    // 使用工厂函数创建两个实例
    const instance1 = getI18nInstanceForLanguage('zh');
    const instance2 = getI18nInstanceForLanguage('zh');
    
    expect(instance1).not.toBe(instance2);
    expect(instance1.language).toBe('zh');
    expect(instance2.language).toBe('zh');
  });
  
  it('每个SSG实例应该有自己的语言设置', () => {
    const enInstance = createI18nInstanceForSSG('en');
    const zhInstance = createI18nInstanceForSSG('zh');
    
    expect(enInstance.language).toBe('en');
    expect(zhInstance.language).toBe('zh');
    
    // 更改一个实例的语言不应影响另一个实例
    enInstance.changeLanguage('fr');
    
    expect(enInstance.language).toBe('fr');
    expect(zhInstance.language).toBe('zh');
  });
});

/**
 * 测试客户端环境下的i18n实例复用
 * 在客户端，相同语言的请求应该复用同一个实例
 */
describe('i18n 在客户端环境下的行为', () => {
  beforeEach(() => {
    // 确保window对象存在
    if (!global.window) {
      // @ts-ignore
      global.window = {
        location: { pathname: '/' },
        __I18N_INSTANCES__: {}
      };
    }
  });
  
  it('在客户端，相同语言的请求应该复用i18n实例', () => {
    // 第一次创建
    const instance1 = getI18nInstanceForLanguage('fr');
    // 第二次获取应该复用实例
    const instance2 = getI18nInstanceForLanguage('fr');
    
    expect(instance1).toBe(instance2);
    expect(instance1.language).toBe('fr');
  });
  
  it('不同语言的请求应该使用不同的实例', () => {
    const frInstance = getI18nInstanceForLanguage('fr');
    const deInstance = getI18nInstanceForLanguage('de');
    
    expect(frInstance).not.toBe(deInstance);
    expect(frInstance.language).toBe('fr');
    expect(deInstance.language).toBe('de');
  });
}); 