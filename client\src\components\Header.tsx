import { useEffect } from 'react'; // useState removed as it's no longer directly used
import { useTranslation } from 'react-i18next';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Link } from 'react-router-dom';
import { getClientI18nInstance, getI18nInstanceForLanguage } from '@/lib/i18n';
import { useLanguageSync } from '@/hooks/use-language-sync';

const Header = () => {
  const { t, i18n } = useTranslation();
  
  // 使用语言同步钩子确保组件语言与URL路径一致
  const { currentLanguage: syncedLanguage } = useLanguageSync('Header');

  // 支持的语言列表
  const supportedLanguages = ['en', 'es', 'fr', 'de', 'zh'];
  
  // 语言代码到语言名称的映射
  const languageNames = {
    en: 'English',
    es: 'Español',
    fr: 'Français',
    de: 'Deutsch',
    zh: '中文'
  };

  // 使用同步后的语言作为状态初始值 - REMOVED
  // const [currentLanguage, setCurrentLanguage] = useState(syncedLanguage);

  // 同步语言并处理静态选择器
  useEffect(() => {
    // 存储事件处理函数的引用，以便正确移除
    const handleLanguageChange = (e: Event) => {
      const newLang = (e.target as HTMLSelectElement).value;
      changeLanguage(newLang);
    };
    
    if (typeof window !== 'undefined') {
      // REMOVED: Logic to sync currentLanguage with syncedLanguage

      // 为静态页面中的语言选择器添加事件处理
      const staticSelects = document.querySelectorAll('select[data-lang-select="true"]');
      staticSelects.forEach(select => {
        const selectElement = select as HTMLSelectElement;
        if (!selectElement.hasAttribute('data-initialized')) {
          // 确保选择器显示正确的当前语言
          if (selectElement.value !== syncedLanguage) {
            selectElement.value = syncedLanguage;
          }
          
          selectElement.addEventListener('change', handleLanguageChange);
          selectElement.setAttribute('data-initialized', 'true');
        } else {
          // 如果已初始化，确保其值与 syncedLanguage 同步
          if (selectElement.value !== syncedLanguage) {
            selectElement.value = syncedLanguage;
          }
        }
      });
    }
    
    // 清理函数
    return () => {
      if (typeof window !== 'undefined') {
        const staticSelects = document.querySelectorAll('select[data-lang-select="true"]');
        staticSelects.forEach(select => {
          select.removeEventListener('change', handleLanguageChange);
        });
      }
    };
  }, [syncedLanguage]); // Dependency is now only syncedLanguage


  // 改进的语言切换函数，更好地支持SSG和静态页面
  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang).then(() => {
      if (typeof window !== 'undefined') {
        let currentPath = window.location.pathname;
        // 规范化 currentPath：如果不是根路径，则删除末尾的斜杠，以简化拆分和连接
        // 保持根路径为 '/'
        if (currentPath !== '/' && currentPath.endsWith('/')) {
          currentPath = currentPath.slice(0, -1);
        }
        const pathParts = currentPath.split('/').filter(Boolean);

        let newPathParts = [...pathParts];
        let newPath;

        if (lang === 'en') {
          // 如果当前路径的第一部分是支持的语言代码，则将其删除
          if (newPathParts.length > 0 && supportedLanguages.includes(newPathParts[0])) {
            newPathParts.shift();
          }
        } else { // 对于非英语语言
          // 如果第一部分是支持的语言代码，则替换它
          if (newPathParts.length > 0 && supportedLanguages.includes(newPathParts[0])) {
            newPathParts[0] = lang;
          } else {
            // 否则，在前面添加新的语言代码
            newPathParts.unshift(lang);
          }
        }

        if (newPathParts.length === 0) {
          newPath = '/';
        } else {
          newPath = '/' + newPathParts.join('/');
        }

        // 如果项目约定使用末尾斜杠（从原始代码看似乎是这样），并且不是根路径，则添加末尾斜杠
        if (newPath !== '/' && !newPath.endsWith('/')) {
          newPath += '/';
        }
        
        // 确保URL不会有双斜杠 (以防万一，尽管上面的逻辑应该能避免)
        newPath = newPath.replace(/\/\/\//g, '/');

        // 仅当路径实际更改时才导航，以防止不必要的重新加载
        // 如果需要保留或清除查询参数/哈希，也需要考虑。
        // 为简单起见，当前逻辑仅更改路径。
        if (window.location.pathname !== newPath || window.location.search || window.location.hash) {
            window.location.href = newPath;
        } 
      }
    }).catch(err => {
      if (process.env.NODE_ENV !== 'production') {
        console.error("[Header] Error changing language or navigating:", err);
      }
    });
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 py-3 md:py-4 flex flex-col md:flex-row justify-between items-center">
        <div className="flex items-center mb-3 md:mb-0 w-full md:w-auto justify-between">
          <div className="flex items-center">
            <img
              src="/logo.svg"
              alt="Text Case Converter Logo"
              className="w-7 h-7 md:w-8 md:h-8 mr-2"
              width="32"
              height="32"
              loading="eager"
            />
            <h1 className="text-xl md:text-2xl font-bold text-primary">
              <Link to={syncedLanguage === 'en' ? '/' : `/${syncedLanguage}/`}>
                <span className="hidden sm:inline">Text Case Converter</span>
                <span className="sm:hidden">TextCase</span>
              </Link>
            </h1>
          </div>

          {/* 移动端显示的Blog按钮和语言选择器 */}
          <div className="md:hidden flex items-center gap-2">
            {/* Blog按钮 */}
            <Link
              to={syncedLanguage === 'en' ? '/blog' : `/${syncedLanguage}/blog`}
              className="px-3 py-1 text-sm font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 flex items-center"
            >
              <span className="material-icons text-sm mr-1">article</span>
              {t('navigation.blog')}
            </Link>

            <Select value={syncedLanguage} onValueChange={changeLanguage}>
            <SelectTrigger className="w-[100px] bg-white hover:bg-gray-100 text-text px-2 py-1 rounded-md border border-gray-200 flex items-center">
              <span className="material-icons text-sm mr-1">language</span>
              <SelectValue placeholder={t('language.selectLanguage')}>
                {languageNames[syncedLanguage as keyof typeof languageNames]}
              </SelectValue>
            </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Español</SelectItem>
                <SelectItem value="fr">Français</SelectItem>
                <SelectItem value="de">Deutsch</SelectItem>
                <SelectItem value="zh">中文</SelectItem>
              </SelectContent>
            </Select>
            {/* 为静态页面添加一个选择器，在 JavaScript 禁用时显示 */}
            <noscript>
              <form method="get" action="" className="m-0 p-0">
                <select
                  className="w-[100px] bg-white hover:bg-gray-100 text-text px-2 py-1 rounded-md border border-gray-200"
                  data-lang-select="true"
                  defaultValue={syncedLanguage}
                  name="lang"
                  onChange={(e) => {
                    // 这个事件处理器在noscript中不会执行，但为了完整性保留
                    const form = e.target.closest('form');
                    if (form) {
                      const newLang = e.target.value;
                      form.action = newLang === 'en' ? '/' : `/${newLang}/`;
                      form.submit();
                    }
                  }}
                >
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                  <option value="zh">中文</option>
                </select>
                <noscript>
                  <button type="submit" className="hidden">Change Language</button>
                </noscript>
              </form>
            </noscript>
            {/* 为静态页面添加一个隐藏的选择器，在 JavaScript 启用时使用 */}
            <select
              className="hidden"
              data-lang-select="true"
              defaultValue={syncedLanguage}
            >
                <option value="en">English</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="zh">中文</option>
            </select>
          </div>
        </div>

        {/* 桌面端显示的Blog按钮和语言选择器 */}
        <div className="hidden md:flex flex-wrap gap-2 md:gap-4 items-center">
          {/* Blog按钮 */}
          <Link
            to={syncedLanguage === 'en' ? '/blog' : `/${syncedLanguage}/blog`}
            className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 flex items-center"
          >
            <span className="material-icons text-sm mr-1">article</span>
            {t('navigation.blog')}
          </Link>

          <Select value={syncedLanguage} onValueChange={changeLanguage}>
            <SelectTrigger className="w-[140px] bg-white hover:bg-gray-100 text-text px-2 py-1 rounded-md border border-gray-200 flex items-center">
              <span className="material-icons text-sm mr-1">language</span>
              <SelectValue placeholder={t('language.selectLanguage')}>
                {languageNames[syncedLanguage as keyof typeof languageNames]}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="es">Español</SelectItem>
              <SelectItem value="fr">Français</SelectItem>
              <SelectItem value="de">Deutsch</SelectItem>
              <SelectItem value="zh">中文</SelectItem>
            </SelectContent>
          </Select>
          {/* 为静态页面添加一个选择器，在 JavaScript 禁用时显示 */}
          <noscript>
            <form method="get" action="" className="m-0 p-0">
              <select
                className="w-[140px] bg-white hover:bg-gray-100 text-text px-2 py-1 rounded-md border border-gray-200"
                data-lang-select="true"
                defaultValue={syncedLanguage}
                name="lang"
                onChange={(e) => {
                  // 这个事件处理器在noscript中不会执行，但为了完整性保留
                  const form = e.target.closest('form');
                  if (form) {
                    const newLang = e.target.value;
                    form.action = newLang === 'en' ? '/' : `/${newLang}/`;
                    form.submit();
                  }
                }}
              >
                <option value="en">English</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="zh">中文</option>
              </select>
              <noscript>
                <button type="submit" className="hidden">Change Language</button>
              </noscript>
            </form>
          </noscript>
          {/* 为静态页面添加一个隐藏的选择器，在 JavaScript 启用时使用 */}
          <select
            className="hidden"
            data-lang-select="true"
            defaultValue={syncedLanguage}
          >
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="zh">中文</option>
          </select>
        </div>
      </div>
    </header>
  );
};

export default Header;
