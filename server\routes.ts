import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import path from "path";
import fs from "fs";

// Translation utils for server-side operations
const translations: { [key: string]: any } = {};

// Load translations from files
const loadTranslations = () => {
  const localesDir = path.resolve(process.cwd(), "client/src/locales");
  
  try {
    const localeFiles = fs.readdirSync(localesDir);
    
    localeFiles.forEach((file) => {
      if (file.endsWith('.json')) {
        const locale = file.replace('.json', '');
        const content = fs.readFileSync(path.join(localesDir, file), 'utf8');
        translations[locale] = JSON.parse(content);
      }
    });
    
    console.log(`Loaded translations for: ${Object.keys(translations).join(', ')}`);
  } catch (error) {
    console.error('Error loading translations:', error);
  }
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Load translations for server-side operations
  loadTranslations();

  // API endpoint to get available languages
  app.get('/api/languages', (req, res) => {
    const languages = Object.keys(translations).map(code => {
      return {
        code,
        name: translations[code]?.language?.selectLanguage || code
      };
    });
    
    res.json({ languages });
  });

  // API endpoint for text conversion
  app.post('/api/convert', (req, res) => {
    const { text, caseType, language = 'en' } = req.body;
    
    if (!text || !caseType) {
      return res.status(400).json({ 
        error: translations[language]?.errors?.missingParameters || 'Missing required parameters' 
      });
    }

    try {
      let result;
      
      switch (caseType) {
        case 'sentence':
          result = text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
          break;
        case 'lower':
          result = text.toLowerCase();
          break;
        case 'upper':
          result = text.toUpperCase();
          break;
        case 'capitalized':
          result = text
            .toLowerCase()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
          break;
        case 'alternating':
          result = text
            .split('')
            .map((char, index) => index % 2 === 0 ? char.toLowerCase() : char.toUpperCase())
            .join('');
          break;
        case 'title':
          const minorWords = ['a', 'an', 'the', 'and', 'but', 'or', 'for', 'nor', 'on', 'at', 'to', 'from', 'by', 'with', 'in', 'of'];
          result = text
            .toLowerCase()
            .split(' ')
            .map((word, index) => {
              if (index === 0 || !minorWords.includes(word)) {
                return word.charAt(0).toUpperCase() + word.slice(1);
              }
              return word;
            })
            .join(' ');
          break;
        case 'inverse':
          result = text
            .split('')
            .map(char => {
              if (char === char.toUpperCase()) {
                return char.toLowerCase();
              }
              return char.toUpperCase();
            })
            .join('');
          break;
        default:
          result = text;
      }
      
      res.json({ result });
    } catch (error) {
      res.status(500).json({ 
        error: translations[language]?.errors?.processingError || 'Error processing text' 
      });
    }
  });

  // API endpoint for text statistics
  app.post('/api/stats', (req, res) => {
    const { text } = req.body;
    
    if (!text && text !== '') {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    try {
      const characterCount = text.length;
      const wordCount = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
      const sentenceCount = text.trim() === '' ? 0 : text.trim().split(/[.!?]+/).filter(Boolean).length;
      const lineCount = text.trim() === '' ? 0 : text.trim().split(/\r\n|\r|\n/).length;
      
      res.json({
        characterCount,
        wordCount,
        sentenceCount,
        lineCount
      });
    } catch (error) {
      res.status(500).json({ error: 'Error processing text' });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
