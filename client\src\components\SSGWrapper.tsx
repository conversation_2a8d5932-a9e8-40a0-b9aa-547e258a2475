import React, { ReactNode, useEffect } from 'react';
import { I18nextProvider } from 'react-i18next';
import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';

// 导入语言包
import enTranslation from '../locales/en.json';
import esTranslation from '../locales/es.json';
import frTranslation from '../locales/fr.json';
import deTranslation from '../locales/de.json';
import zhTranslation from '../locales/zh.json';

// 定义支持的语言类型
type SupportedLanguage = 'en' | 'es' | 'fr' | 'de' | 'zh';

interface SSGWrapperProps {
  children: ReactNode;
  routePath?: string;
  isClient?: boolean;
}

/**
 * SSG包装组件，确保每个路由使用正确的i18n实例
 * 这个组件在SSG过程中为每个路由创建一个独立的i18n实例
 */
const SSGWrapper: React.FC<SSGWrapperProps> = ({ children, routePath, isClient }) => {
  // 根据路由路径确定语言
  const getLanguageFromPath = (path?: string): SupportedLanguage => {
    if (!path) return 'en';

    if (path.startsWith('/en')) return 'en';
    if (path.startsWith('/es')) return 'es';
    if (path.startsWith('/fr')) return 'fr';
    if (path.startsWith('/de')) return 'de';
    if (path.startsWith('/zh')) return 'zh';

    return 'en';
  };

  // 获取当前路由的语言
  const lang = getLanguageFromPath(routePath);

  // 为当前路由获取或创建一个独立的i18n实例
  const i18nInstance = React.useMemo(() => {
    // 在客户端，使用全局i18n实例
    if (isClient) {
      return i18next;
    }

    // 在SSG过程中，优先使用main.tsx中为当前路由创建的实例
    if ((i18next as any).__current_route_instance &&
        (i18next as any).__current_route_path === routePath) {
      const instance = (i18next as any).__current_route_instance;
      console.log(`[SSGWrapper] 使用已创建的i18n实例，路径: ${routePath}，语言: ${instance.language}，实例ID: ${(instance as any).__language_id}`);
      return instance;
    }

    // 如果没有找到当前路由的实例，创建一个新的
    console.log(`[SSGWrapper] 为路径 ${routePath} 创建新的i18n实例，语言: ${lang}`);

    const instance = i18next.createInstance();

    instance
      .use(initReactI18next)
      .init({
        resources: {
          en: { translation: enTranslation },
          es: { translation: esTranslation },
          fr: { translation: frTranslation },
          de: { translation: deTranslation },
          zh: { translation: zhTranslation }
        },
        lng: lang,
        fallbackLng: 'en',
        interpolation: {
          escapeValue: false
        },
        react: {
          useSuspense: false
        }
      });

    // 强制设置语言，确保初始化后语言正确
    instance.changeLanguage(lang);

    // 添加实例标识，方便调试
    (instance as any).__language_id = `ssg_${lang}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    (instance as any).__route = routePath;

    // 保存实例，供其他组件使用
    (i18next as any).__current_route_instance = instance;
    (i18next as any).__current_route_path = routePath;
    (i18next as any).__current_route_language = lang;

    return instance;
  }, [routePath, lang, isClient]);

  // 确保语言设置正确
  useEffect(() => {
    if (i18nInstance.language !== lang) {
      console.log(`[SSGWrapper] 修正语言: 从 ${i18nInstance.language} 到 ${lang}`);
      i18nInstance.changeLanguage(lang);
    }
  }, [i18nInstance, lang]);

  // 使用I18nextProvider包装子组件，确保它们使用正确的i18n实例
  return (
    <I18nextProvider i18n={i18nInstance}>
      {children}
    </I18nextProvider>
  );
};

export default SSGWrapper;
