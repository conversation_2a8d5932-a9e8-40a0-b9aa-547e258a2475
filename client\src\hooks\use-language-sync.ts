import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

// Helper function to get language from URL path
const getLanguageFromPath = () => {
  if (typeof window === 'undefined') {
    return 'en'; // Default for SSR/SSG or if window is not available
  }
  const pathParts = window.location.pathname.split('/').filter(Boolean);
  if (pathParts.length > 0) {
    const firstPart = pathParts[0];
    // Ensure this list matches supportedLanguages in Header or a central config
    if (['en', 'es', 'fr', 'de', 'zh'].includes(firstPart)) {
      return firstPart;
    }
  }
  return 'en'; // Default language if no valid language code is found in path
};

/**
 * 自定义hook，用于确保组件的语言显示与URL路径一致，并同步i18next实例。
 * @param componentName 组件名称，用于日志
 * @returns currentLanguage 当前路径对应的语言代码
 */
export function useLanguageSync(componentName: string) {
  const { i18n } = useTranslation();
  const pathLang = getLanguageFromPath(); // Get language from path directly
  const isChangingRef = useRef(false);
  const debounceTimerRef = useRef<number>();

  // 在组件挂载时立即同步语言，这对SSG特别重要
  useEffect(() => {
    // 确保i18n实例存在且语言需要更新，并且没有正在进行的语言切换
    if (!i18n || i18n.language === pathLang || isChangingRef.current) return;

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[${componentName}] (useLanguageSync Effect): i18n language (${i18n.language}) differs from path language (${pathLang}). Syncing i18n.`);
    }

    // 在SSG/SSR环境下通过页面跳转来处理语言切换
    if (typeof window === 'undefined') {
      return; // 在服务端不做任何处理
    }

    // 如果是客户端且需要切换语言，直接通过页面跳转
    const currentPath = window.location.pathname;
    const currentLangPrefix = `/${i18n.language}/`;
    const newLangPrefix = `/${pathLang}/`;
    
    if (currentPath.startsWith(currentLangPrefix)) {
      // 替换当前语言前缀为新的语言前缀
      const newPath = currentPath.replace(currentLangPrefix, newLangPrefix);
      window.location.href = newPath;
      return;
    } else if (!currentPath.startsWith('/')) {
      // 如果路径不是以/开头，添加新的语言前缀
      window.location.href = newLangPrefix + currentPath;
      return;
    }

    // 如果当前路径没有语言前缀，添加默认语言前缀
    window.location.href = `/${pathLang}${currentPath}`;
    return;
  }, [i18n, pathLang, componentName]);

  // 返回当前路径对应的语言代码
  return { currentLanguage: pathLang };
}