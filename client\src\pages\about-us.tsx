import React from 'react';

const AboutUs: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h1 className="text-3xl font-bold mb-6">About Us</h1>

        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
          <p className="text-gray-700 text-lg leading-relaxed">
            Text Case Converter is dedicated to providing users with the simplest and most efficient text conversion tools.
            We believe that excellent tools should be free, easy to use, and truly improve user productivity.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
          <p className="text-gray-700 mb-4">If you have any questions, suggestions, or feedback, please feel free to contact us:</p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <div>
                <span className="font-semibold text-gray-700">Email Address: </span>
                <a href="mailto:<EMAIL>" className="text-primary hover:text-primary-dark font-medium">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};



export default AboutUs;
