import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const Footer = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    document.documentElement.setAttribute('lang', lang);

    // 构建新的URL路径
    const pathParts = window.location.pathname.split('/');

    // 如果当前路径已经包含语言代码，则替换它
    if (pathParts.length > 1 && ['en', 'es', 'fr', 'de'].includes(pathParts[1])) {
      pathParts[1] = lang;
    } else {
      // 否则在路径开头添加语言代码
      pathParts.splice(1, 0, lang);
    }

    // 如果是默认语言(英语)且在首页，则不显示语言路径
    if (lang === 'en' && (pathParts.length <= 2 || pathParts[2] === '')) {
      navigate('/');
    } else {
      navigate(pathParts.join('/'));
    }
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-800 text-white pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          <div>
            <div className="flex items-center mb-4">
              <img src="/logo.svg" alt="Text Case Converter Logo" className="w-8 h-8 mr-2 bg-white rounded-full" />
              <h3 className="text-lg font-semibold">Text Case Converter</h3>
            </div>
            <p className="text-gray-300 mb-4">{t('footer.description')}</p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-white" aria-label="Website">
                <span className="material-icons">language</span>
              </a>
              <a href="#" className="text-gray-300 hover:text-white" aria-label="GitHub">
                <span className="material-icons">code</span>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.popularTools')}</h3>
            <ul className="space-y-2 text-gray-300">
              <li><a href="#" className="hover:text-white">{t('tools.textCaseConverter')}</a></li>
              <li><a href="#" className="hover:text-white">{t('tools.loremIpsumGenerator')}</a></li>
              <li><a href="#" className="hover:text-white">{t('tools.characterCountTool')}</a></li>
              <li><a href="#" className="hover:text-white">{t('tools.wordCountTool')}</a></li>
              <li><a href="#" className="hover:text-white">{t('tools.reverseTextGenerator')}</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.resources')}</h3>
            <ul className="space-y-2 text-gray-300">
              <li><a href="#" className="hover:text-white">{t('resources.blog')}</a></li>
              <li><a href="#" className="hover:text-white">{t('resources.helpCenter')}</a></li>
              <li><a href="#" className="hover:text-white">{t('resources.apiDocumentation')}</a></li>
              <li><a href="#" className="hover:text-white">{t('resources.suggestTool')}</a></li>
              <li><a href="#" className="hover:text-white">{t('resources.chromeExtension')}</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.language')}</h3>
            <ul className="space-y-2 text-gray-300">
              <li><a href="#" onClick={(e) => { e.preventDefault(); changeLanguage('en'); }} className="hover:text-white">English</a></li>
              <li><a href="#" onClick={(e) => { e.preventDefault(); changeLanguage('es'); }} className="hover:text-white">Español</a></li>
              <li><a href="#" onClick={(e) => { e.preventDefault(); changeLanguage('fr'); }} className="hover:text-white">Français</a></li>
              <li><a href="#" onClick={(e) => { e.preventDefault(); changeLanguage('de'); }} className="hover:text-white">Deutsch</a></li>
              <li><a href="#" onClick={(e) => { e.preventDefault(); changeLanguage('pl'); }} className="hover:text-white">Polski</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 pt-6 text-sm text-gray-400">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p>© {currentYear} TextCase. {t('footer.allRightsReserved')}</p>
            <div className="mt-4 md:mt-0 space-x-4">
              <a href="#" className="hover:text-white">{t('footer.privacyPolicy')}</a>
              <a href="#" className="hover:text-white">{t('footer.termsOfService')}</a>
              <a href="#" className="hover:text-white">{t('footer.contactUs')}</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
