/* 浏览器兼容性样式 */

/* 确保所有元素的盒模型一致 */
*, *::before, *::after {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

/* 移动设备上的点击高亮颜色 */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
}

/* 确保图像和媒体元素响应式 */
img, video, canvas, audio, iframe, embed, object {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 修复旧版IE的SVG缩放问题 */
svg:not(:root) {
  overflow: hidden;
}

/* 确保表单元素在所有浏览器中的一致性 */
button, input, optgroup, select, textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

/* 修复iOS中的按钮样式 */
button, [type="button"], [type="reset"], [type="submit"] {
  -webkit-appearance: button;
}

/* 修复Firefox中的按钮样式 */
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, 
[type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/* 修复Edge和IE中的文本区域溢出 */
textarea {
  overflow: auto;
}

/* 修复iOS中的文本大小调整 */
body {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* 修复移动设备上的滚动问题 */
html, body {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 修复Safari中的字体渲染问题 */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 修复IE中的主要容器宽度问题 */
.container {
  width: 100%;
  max-width: 100%;
}

/* 媒体查询 - 移动设备优化 */
@media screen and (max-width: 768px) {
  /* 调整移动设备上的字体大小 */
  html {
    font-size: 14px;
  }
  
  /* 确保按钮和交互元素有足够的点击区域 */
  button, 
  .btn, 
  a, 
  input[type="button"], 
  input[type="submit"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* 调整移动设备上的间距 */
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  /* 确保表单元素在移动设备上易于使用 */
  input, select, textarea {
    font-size: 16px; /* 防止iOS上的缩放 */
  }
}

/* 打印样式优化 */
@media print {
  body {
    background-color: #fff;
    color: #000;
  }
  
  a {
    text-decoration: underline;
  }
  
  /* 隐藏不需要打印的元素 */
  .no-print, 
  nav, 
  footer, 
  button, 
  .btn {
    display: none !important;
  }
  
  /* 确保内容不被截断 */
  @page {
    margin: 2cm;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: 20 14.3% 4.1%;
    --foreground: 60 9.1% 97.8%;
    --card: 20 14.3% 4.1%;
    --card-foreground: 60 9.1% 97.8%;
    --popover: 20 14.3% 4.1%;
    --popover-foreground: 60 9.1% 97.8%;
    --primary: 207 90% 54%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 122 39% 49%;
    --secondary-foreground: 60 9.1% 97.8%;
    --muted: 12 6.5% 15.1%;
    --muted-foreground: 24 5.4% 63.9%;
    --accent: 36 100% 50%;
    --accent-foreground: 60 9.1% 97.8%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 12 6.5% 15.1%;
    --input: 12 6.5% 15.1%;
    --ring: 207 90% 54%;
  }
}
