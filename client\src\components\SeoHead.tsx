import React from 'react';
import { Head } from 'vite-react-ssg';
import { useTranslation } from 'react-i18next';
import { useLanguageSync } from '@/hooks/use-language-sync';

interface SeoHeadProps {
  title?: string;
  description?: string;
  path?: string;
  noindex?: boolean;
}

const SeoHead: React.FC<SeoHeadProps> = ({
  title,
  description,
  path = '',
  noindex = false
}) => {
  const { t } = useTranslation();
  // 使用自定义hook确保语言同步
  const { currentLanguage } = useLanguageSync('SeoHead');
  
  const baseUrl = 'https://textcase.top';
  
  // 构建当前URL，处理语言前缀
  let currentPath = path;
  // 以语言代码开头的路径(如 /zh/...)
  if (currentLanguage !== 'en' && !path.startsWith(`/${currentLanguage}`)) {
    // 如果是根路径或者不是以语言代码开头的路径
    if (path === '/' || !path.startsWith('/')) {
      currentPath = `/${currentLanguage}/`;
    } else {
      // 其他页面，添加语言前缀
      currentPath = `/${currentLanguage}${path}`;
    }
  }
  
  const currentUrl = `${baseUrl}${currentPath}`;

  // 支持的语言列表
  const supportedLanguages = ['en', 'es', 'fr', 'de', 'zh'];

  // 使用传入的标题和描述，如果没有则使用翻译中的默认值
  const pageTitle = title || t('meta.title');
  const pageDescription = description || t('meta.description');

  return (
    <Head>
      {/* 基本元标签 */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />

      {/* 规范链接 - 告诉搜索引擎这是首选URL */}
      <link rel="canonical" href={currentUrl} />

      {/* Hreflang 标签 - 告诉搜索引擎不同语言版本的URL */}
      {supportedLanguages.map(lang => {
        // 构建语言特定的URL
        let langPath = path;
        // 如果路径已经包含语言代码，则替换它
        if (langPath.startsWith('/') && langPath.length > 1) {
          const pathParts = langPath.split('/');
          if (pathParts.length > 1 && supportedLanguages.includes(pathParts[1])) {
            pathParts[1] = lang;
            langPath = pathParts.join('/');
          } else {
            // 否则在路径开头添加语言代码
            langPath = lang === 'en' ? langPath : `/${lang}${langPath}`;
          }
        } else {
          // 首页情况
          langPath = lang === 'en' ? '/' : `/${lang}/`;
        }

        return (
          <link
            key={lang}
            rel="alternate"
            hrefLang={lang}
            href={`${baseUrl}${langPath}`}
          />
        );
      })}

      {/* 默认语言版本 */}
      <link rel="alternate" hrefLang="x-default" href={baseUrl} />

      {/* Open Graph 标签 */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:type" content="website" />
      <meta property="og:locale" content={currentLanguage} />

      {/* Twitter 卡片 */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />

      {/* 如果页面不应被索引 */}
      {noindex && <meta name="robots" content="noindex, nofollow" />}

      {/* 设置当前语言 */}
      <html lang={currentLanguage} />
    </Head>
  );
};

export default SeoHead;
