import React from 'react';

interface MockAdProps {
  /** 广告位置 */
  position: 'top' | 'bottom' | 'sidebar' | 'content';
  /** 广告格式 */
  format: 'auto' | 'rectangle' | 'horizontal' | 'vertical';
  /** 额外的CSS类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

/**
 * 模拟广告组件 - 仅在开发环境中使用
 * 用于预览广告位的位置和大小
 */
const MockAd: React.FC<MockAdProps> = ({ 
  position, 
  format, 
  className = '', 
  style = {} 
}) => {
  // 根据格式获取默认尺寸
  const getDimensions = () => {
    switch (format) {
      case 'horizontal':
        return { width: '100%', height: '90px' };
      case 'rectangle':
        return { width: '300px', height: '250px' };
      case 'vertical':
        return { width: '160px', height: '600px' };
      default:
        return { width: '100%', height: '90px' };
    }
  };

  const dimensions = getDimensions();
  
  // 根据位置获取描述文本
  const getDescription = () => {
    switch (position) {
      case 'top':
        return '顶部横幅广告位';
      case 'bottom':
        return '底部横幅广告位';
      case 'sidebar':
        return '侧边栏广告位';
      case 'content':
        return '内容中间广告位';
      default:
        return '广告位';
    }
  };

  const mockStyle: React.CSSProperties = {
    ...dimensions,
    backgroundColor: '#f3f4f6',
    border: '2px dashed #d1d5db',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    color: '#6b7280',
    fontSize: '14px',
    fontWeight: '500',
    textAlign: 'center',
    margin: '0 auto',
    ...style
  };

  return (
    <div className={`mock-ad mock-ad-${position} ${className}`} style={mockStyle}>
      <div style={{ marginBottom: '8px' }}>
        📢 {getDescription()}
      </div>
      <div style={{ fontSize: '12px', opacity: 0.7 }}>
        {dimensions.width} × {dimensions.height}
      </div>
      <div style={{ fontSize: '11px', opacity: 0.5, marginTop: '4px' }}>
        (开发环境模拟)
      </div>
    </div>
  );
};

export default MockAd;
