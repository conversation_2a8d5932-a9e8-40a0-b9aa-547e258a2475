import { useTranslation } from 'react-i18next';

interface ToolItem {
  key: string;
  icon: string;
  title: string;
  description: string;
}

const AdditionalTools = () => {
  const { t } = useTranslation();

  const tools: ToolItem[] = [
    {
      key: 'reverse',
      icon: 'text_fields',
      title: t('additionalTools.reverse.title'),
      description: t('additionalTools.reverse.description')
    },
    {
      key: 'upsideDown',
      icon: 'swap_vert',
      title: t('additionalTools.upsideDown.title'),
      description: t('additionalTools.upsideDown.description')
    },
    {
      key: 'morseCode',
      icon: 'code',
      title: t('additionalTools.morseCode.title'),
      description: t('additionalTools.morseCode.description')
    },
    {
      key: 'smallText',
      icon: 'format_size',
      title: t('additionalTools.smallText.title'),
      description: t('additionalTools.smallText.description')
    },
    {
      key: 'strikethrough',
      icon: 'format_strikethrough',
      title: t('additionalTools.strikethrough.title'),
      description: t('additionalTools.strikethrough.description')
    },
    {
      key: 'wideText',
      icon: 'space_bar',
      title: t('additionalTools.wideText.title'),
      description: t('additionalTools.wideText.description')
    }
  ];

  return (
    <section id="tools" className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-xl font-bold mb-6">{t('additionalTools.title')}</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tools.map((tool) => (
          <a key={tool.key} href="#" className="tool-card">
            <span className="material-icons text-primary mr-3">{tool.icon}</span>
            <div>
              <h3 className="font-medium">{tool.title}</h3>
              <p className="text-sm text-gray-600">{tool.description}</p>
            </div>
          </a>
        ))}
      </div>
      
      <div className="mt-6 text-center">
        <a href="#" className="inline-flex items-center text-primary hover:text-primary-dark">
          <span>{t('additionalTools.viewAll')}</span>
          <span className="material-icons text-sm ml-1">arrow_forward</span>
        </a>
      </div>
    </section>
  );
};

export default AdditionalTools;
