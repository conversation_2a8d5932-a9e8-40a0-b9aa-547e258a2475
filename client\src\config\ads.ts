// 广告配置
export interface AdConfig {
  /** 广告位ID */
  slot: string;
  /** 广告格式 */
  format: 'auto' | 'rectangle' | 'horizontal' | 'vertical';
  /** 是否响应式 */
  responsive: boolean;
  /** 广告位置描述 */
  description: string;
}

// 广告位配置
export const AD_SLOTS: Record<string, AdConfig> = {
  // 页面顶部横幅广告
  TOP_BANNER: {
    slot: '1234567890', // 替换为您的实际广告位ID
    format: 'horizontal',
    responsive: true,
    description: '页面顶部横幅广告'
  },
  
  // 页面底部横幅广告
  BOTTOM_BANNER: {
    slot: '0987654321', // 替换为您的实际广告位ID
    format: 'horizontal',
    responsive: true,
    description: '页面底部横幅广告'
  },
  
  // 侧边栏广告 (备用)
  SIDEBAR: {
    slot: '1122334455', // 替换为您的实际广告位ID
    format: 'rectangle',
    responsive: true,
    description: '侧边栏广告'
  },
  
  // 内容中间广告 (备用)
  CONTENT_MIDDLE: {
    slot: '5544332211', // 替换为您的实际广告位ID
    format: 'rectangle',
    responsive: true,
    description: '内容中间广告'
  }
};

// AdSense 客户端配置
export const ADSENSE_CONFIG = {
  // 客户端ID (请替换为您的实际客户端ID)
  CLIENT_ID: import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-xxxxxxxxxxxxxxxxx',

  // 是否启用广告 (可以通过环境变量控制)
  ENABLED: import.meta.env.VITE_ADSENSE_ENABLED !== 'false',

  // 开发环境是否显示广告
  SHOW_IN_DEV: import.meta.env.VITE_ADSENSE_SHOW_IN_DEV === 'true'
};

// 检查是否应该显示广告
export const shouldShowAds = (): boolean => {
  // 在生产环境中始终显示广告（如果启用）
  if (import.meta.env.MODE === 'production') {
    return ADSENSE_CONFIG.ENABLED;
  }

  // 在开发环境中根据配置决定是否显示
  return ADSENSE_CONFIG.ENABLED && ADSENSE_CONFIG.SHOW_IN_DEV;
};

// 获取广告配置
export const getAdConfig = (slotName: keyof typeof AD_SLOTS): AdConfig | null => {
  if (!shouldShowAds()) {
    return null;
  }
  
  return AD_SLOTS[slotName] || null;
};
