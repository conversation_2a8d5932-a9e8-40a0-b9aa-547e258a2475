import { useTranslation } from 'react-i18next';
import Navigation from '@/components/Navigation';
import TextConverter from '@/components/TextConverter';
import ConversionExplanations from '@/components/ConversionExplanations';
import FAQ from '@/components/FAQ';
import SeoHead from '@/components/SeoHead';
import JsonLd from '@/components/JsonLd';
import { useLanguageSync } from '@/hooks/use-language-sync';

const Home = () => {
  const { t } = useTranslation();
  // 使用自定义hook确保语言同步
  useLanguageSync('Home');

  // 获取FAQ数据用于JSON-LD
  const getFaqData = () => {
    return [
      {
        question: t('faq.why.question'),
        answer: t('faq.why.answer')
      },
      {
        question: t('faq.secure.question'),
        answer: t('faq.secure.answer')
      },
      {
        question: t('faq.mobile.question'),
        answer: t('faq.mobile.answer')
      },
      {
        question: t('faq.languages.question'),
        answer: t('faq.languages.answer')
      }
    ];
  };

  return (
    <>
      {/* SEO头部标签，包含canonical和hreflang */}
      <SeoHead path="/" />

      {/* 添加结构化数据 */}
      <JsonLd type="website" />
      <JsonLd type="organization" />
      <JsonLd type="faq" faqData={getFaqData()} />

      <Navigation />

      <main className="container mx-auto px-4 mb-12">
        <TextConverter />
        <ConversionExplanations />
        <FAQ />
      </main>
    </>
  );
};

export default Home;
