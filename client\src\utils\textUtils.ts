// Text conversion utilities
import i18next from 'i18next';

export interface TextStats {
  characterCount: number;
  wordCount: number;
  sentenceCount: number;
  lineCount: number;
}

export function getTextStats(text: string): TextStats {
  const trimmedText = text.trim();
  
  const characterCount = text.length;
  const wordCount = trimmedText === '' ? 0 : trimmedText.split(/\s+/).length;
  const sentenceCount = trimmedText === '' ? 0 : trimmedText.split(/[.!?。！？]+/).filter(Boolean).length;
  const lineCount = trimmedText === '' ? 0 : trimmedText.split(/\r\n|\r|\n/).length;
  
  return {
    characterCount,
    wordCount,
    sentenceCount,
    lineCount
  };
}

// 用于判断字符类型的辅助函数
const isChinesePunctuation = (char: string): boolean => {
  const chinesePunctuationRegex = /[\u3002\uff1f\uff01\uff0c\u3001\uff1b\uff1a\u201c\u201d\u2018\u2019\uff08\uff09\u300a\u300b\u3008\u3009\u3010\u3011\u300e\u300f\u300c\u300d\ufe43\ufe44\u3014\u3015\u2026\u2014\uff5e\ufe4f\uffe5]/;
  return chinesePunctuationRegex.test(char);
};

const isLatinLetter = (char: string): boolean => {
  return /[a-zA-ZÀ-ÖØ-öø-ÿ]/.test(char);
};

export function toSentenceCase(text: string): string {
  // 获取当前语言
  const currentLanguage = i18next.language || 'en';
  
  // 针对不同语言的句子结束标点符号
  const sentenceEndingPattern = currentLanguage === 'zh' 
    ? /([.!?。！？]\s*)/g 
    : /([.!?]\s*)/g;
  
  // 如果是西里尔/拉丁/希腊字母系统的语言进行标准大小写转换
  if (['en', 'es', 'fr', 'de'].includes(currentLanguage)) {
    // 分割句子
    const sentences = text.split(sentenceEndingPattern).filter(Boolean);
    
    let result = '';
    let shouldCapitalize = true;
    
    for (let i = 0; i < sentences.length; i++) {
      const part = sentences[i];
      
      // 如果是标点符号，直接添加不改变大小写
      if (sentenceEndingPattern.test(part)) {
        result += part;
        shouldCapitalize = true;
        continue;
      }
      
      if (shouldCapitalize) {
        // 找到第一个字母并大写
        const firstLetterMatch = part.match(/[a-zA-ZÀ-ÖØ-öø-ÿ]/);
        if (firstLetterMatch) {
          const index = part.indexOf(firstLetterMatch[0]);
          result += part.substring(0, index) + 
                   part.charAt(index).toUpperCase() + 
                   part.substring(index + 1).toLowerCase();
        } else {
          result += part.toLowerCase();
        }
        shouldCapitalize = false;
      } else {
        result += part.toLowerCase();
      }
    }
    
    return result;
  }
  
  // 中文和其他非拉丁字母语言不需要大小写处理，但可能包含拉丁字母
  if (currentLanguage === 'zh') {
    // 对于中文文本，我们只对其中的拉丁字母部分应用大小写规则
    let result = '';
    let inSentence = false;
    let sentenceStart = 0;
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      // 如果是句子结束符号
      if (/[.!?。！？]/.test(char)) {
        result += text.substring(sentenceStart, i + 1);
        inSentence = false;
        sentenceStart = i + 1;
      } 
      // 如果是新句子开始的拉丁字母
      else if (!inSentence && isLatinLetter(char)) {
        result += text.substring(sentenceStart, i);
        result += char.toUpperCase();
        inSentence = true;
        sentenceStart = i + 1;
      }
    }
    
    // 添加剩余部分
    if (sentenceStart < text.length) {
      result += text.substring(sentenceStart);
    }
    
    return result;
  }
  
  // 默认行为：简单地将第一个字母大写，其余转为小写
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

export function toLowerCase(text: string): string {
  return text.toLowerCase();
}

export function toUpperCase(text: string): string {
  return text.toUpperCase();
}

export function toCapitalizedCase(text: string): string {
  // 获取当前语言
  const currentLanguage = i18next.language || 'en';
  
  // 中文和其他非拉丁字母语言
  if (currentLanguage === 'zh') {
    // 对于中文，仅处理拉丁字母部分
    let result = '';
    let inWord = false;
    let wordStart = 0;
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      
      // 如果是拉丁字母
      if (isLatinLetter(char)) {
        if (!inWord) {
          // 新单词开始
          result += text.substring(wordStart, i);
          result += char.toUpperCase();
          inWord = true;
          wordStart = i + 1;
        }
      } 
      // 如果是空格或标点符号
      else if (/\s/.test(char) || isChinesePunctuation(char)) {
        if (inWord) {
          // 单词结束
          inWord = false;
          wordStart = i;
        }
      }
    }
    
    // 添加剩余部分
    if (wordStart < text.length) {
      result += text.substring(wordStart);
    }
    
    return result;
  }
  
  // 拉丁字母语言使用标准处理
  return text
    .toLowerCase()
    .split(/\s+/)
    .map(word => {
      if (word.length === 0) return word;
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
}

export function toAlternatingCase(text: string): string {
  // 获取当前语言
  const currentLanguage = i18next.language || 'en';
  
  // 中文和其他非拉丁字母语言
  if (currentLanguage === 'zh') {
    // 对于中文，仅处理拉丁字母部分
    let result = '';
    let alternateIndex = 0;
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      
      // 仅对拉丁字母应用交替大小写
      if (isLatinLetter(char)) {
        result += alternateIndex % 2 === 0 ? char.toLowerCase() : char.toUpperCase();
        alternateIndex++;
      } else {
        result += char;
      }
    }
    
    return result;
  }
  
  // 拉丁字母语言使用标准处理
  return text
    .split('')
    .map((char, index) => {
      if (!isLatinLetter(char)) return char;
      return index % 2 === 0 ? char.toLowerCase() : char.toUpperCase();
    })
    .join('');
}

export function toTitleCase(text: string): string {
  // 获取当前语言
  const currentLanguage = i18next.language || 'en';
  
  // 定义不同语言中不需要大写的小词
  const minorWords: Record<string, string[]> = {
    en: ['a', 'an', 'the', 'and', 'but', 'or', 'for', 'nor', 'on', 'at', 'to', 'from', 'by', 'with', 'in', 'of'],
    es: ['y', 'o', 'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas', 'de', 'del', 'a', 'al', 'en', 'por', 'con', 'sin'],
    fr: ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'à', 'au', 'aux', 'en', 'par', 'pour', 'avec', 'sans'],
    de: ['der', 'die', 'das', 'ein', 'eine', 'und', 'oder', 'von', 'mit', 'zu', 'zur', 'zum', 'in', 'im', 'an', 'am', 'auf', 'für']
  };
  
  // 中文和其他非拉丁字母语言
  if (currentLanguage === 'zh') {
    // 对于中文，可以仅处理拉丁字母部分，或保持原状
    return text;
  }
  
  // 使用当前语言的小词列表，如果没有则使用英语的小词列表
  const currentMinorWords = minorWords[currentLanguage] || minorWords.en;
  
  return text
    .toLowerCase()
    .split(/\s+/)
    .map((word, index, array) => {
      // 始终大写第一个和最后一个单词
      if (index === 0 || index === array.length - 1) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }
      // 大写不在小词列表中的单词
      return currentMinorWords.includes(word) 
        ? word 
        : word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
}

export function toInverseCase(text: string): string {
  // 获取当前语言
  const currentLanguage = i18next.language || 'en';
  
  // 中文和其他非拉丁字母语言
  if (currentLanguage === 'zh') {
    // 对于中文，仅处理拉丁字母部分
    let result = '';
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      
      // 仅对拉丁字母应用反转大小写
      if (isLatinLetter(char)) {
        result += char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase();
      } else {
        result += char;
      }
    }
    
    return result;
  }
  
  // 拉丁字母语言使用标准处理
  return text
    .split('')
    .map(char => {
      if (!isLatinLetter(char)) return char;
      return char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase();
    })
    .join('');
}

export const convertTextCase = (text: string, caseType: string): string => {
  switch (caseType) {
    case 'sentence':
      return toSentenceCase(text);
    case 'lower':
      return toLowerCase(text);
    case 'upper':
      return toUpperCase(text);
    case 'capitalized':
      return toCapitalizedCase(text);
    case 'alternating':
      return toAlternatingCase(text);
    case 'title':
      return toTitleCase(text);
    case 'inverse':
      return toInverseCase(text);
    default:
      return text;
  }
};
