import React, { useEffect, useRef } from 'react';

interface AdSenseProps {
  /** 广告位置标识 */
  slot: string;
  /** 广告格式 */
  format?: 'auto' | 'rectangle' | 'horizontal' | 'vertical';
  /** 是否为响应式广告 */
  responsive?: boolean;
  /** 广告样式 */
  style?: React.CSSProperties;
  /** 广告容器的CSS类名 */
  className?: string;
  /** 广告客户端ID (可选，如果未提供则使用环境变量) */
  client?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

const AdSense: React.FC<AdSenseProps> = ({
  slot,
  format = 'auto',
  responsive = true,
  style = {},
  className = '',
  client
}) => {
  const adRef = useRef<HTMLDivElement>(null);
  const isAdLoaded = useRef(false);

  // 默认的广告客户端ID (在生产环境中应该从环境变量获取)
  const adClient = client || import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-xxxxxxxxxxxxxxxxx';

  useEffect(() => {
    // 只在客户端加载广告
    if (typeof window === 'undefined') return;

    // 避免重复加载
    if (isAdLoaded.current) return;

    const loadAd = () => {
      try {
        // 确保 adsbygoogle 数组存在
        if (!window.adsbygoogle) {
          window.adsbygoogle = [];
        }

        // 推送广告配置到 adsbygoogle 队列
        window.adsbygoogle.push({});
        isAdLoaded.current = true;
      } catch (error) {
        console.error('AdSense 加载失败:', error);
      }
    };

    // 检查 AdSense 脚本是否已加载
    const checkAdSenseScript = () => {
      const existingScript = document.querySelector('script[src*="adsbygoogle.js"]');
      
      if (existingScript) {
        // 脚本已存在，直接加载广告
        loadAd();
      } else {
        // 动态加载 AdSense 脚本
        const script = document.createElement('script');
        script.async = true;
        script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adClient}`;
        script.crossOrigin = 'anonymous';
        
        script.onload = () => {
          loadAd();
        };
        
        script.onerror = () => {
          console.error('AdSense 脚本加载失败');
        };
        
        document.head.appendChild(script);
      }
    };

    // 延迟加载广告，避免影响页面性能
    const timer = setTimeout(checkAdSenseScript, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [adClient]);

  // 默认样式
  const defaultStyle: React.CSSProperties = {
    display: 'block',
    textAlign: 'center',
    ...style
  };

  // 根据格式设置特定样式
  if (format === 'horizontal') {
    defaultStyle.width = '100%';
    defaultStyle.height = '90px';
  } else if (format === 'rectangle') {
    defaultStyle.width = '300px';
    defaultStyle.height = '250px';
    defaultStyle.margin = '0 auto';
  }

  return (
    <div 
      ref={adRef}
      className={`adsense-container ${className}`}
      style={{ 
        margin: '20px 0',
        padding: '10px',
        ...defaultStyle
      }}
    >
      <ins
        className="adsbygoogle"
        style={defaultStyle}
        data-ad-client={adClient}
        data-ad-slot={slot}
        data-ad-format={format}
        data-full-width-responsive={responsive ? 'true' : 'false'}
      />
    </div>
  );
};

export default AdSense;
