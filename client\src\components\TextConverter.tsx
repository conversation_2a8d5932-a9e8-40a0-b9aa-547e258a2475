import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from "@/hooks/use-toast";
import { getTextStats, convertTextCase, TextStats } from '../utils/textUtils';
import { downloadTextAsFile, copyToClipboard } from '../utils/fileUtils';
import { Textarea } from "@/components/ui/textarea";
import { useLanguageSync } from '@/hooks/use-language-sync';

type Case = 'sentence' | 'lower' | 'upper' | 'capitalized' | 'alternating' | 'title' | 'inverse';

const TextConverter = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  // 使用自定义hook确保语言同步
  const { currentLanguage } = useLanguageSync('TextConverter');

  const [text, setText] = useState('');
  const [textStats, setTextStats] = useState<TextStats>({
    characterCount: 0,
    wordCount: 0,
    sentenceCount: 0,
    lineCount: 0
  });
  const [selectedCase, setSelectedCase] = useState<Case>('sentence');

  useEffect(() => {
    setTextStats(getTextStats(text));
  }, [text]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);
  };

  const handleCaseChange = (caseType: Case) => {
    setSelectedCase(caseType);
  };

  const handleConvert = () => {
    if (text) {
      const converted = convertTextCase(text, selectedCase);
      setText(converted);
      toast({
        title: t('toast.converted'),
        description: t('toast.convertedDescription'),
      });
    }
  };

  const handleCopy = async () => {
    try {
      await copyToClipboard(text);
      toast({
        title: t('toast.copied'),
        description: t('toast.copiedDescription'),
      });
    } catch (error) {
      toast({
        title: t('toast.copyFailed'),
        description: t('toast.copyFailedDescription'),
        variant: "destructive",
      });
    }
  };

  const handleDownload = () => {
    downloadTextAsFile(text, 'converted-text.txt');
    toast({
      title: t('toast.downloaded'),
      description: t('toast.downloadedDescription'),
    });
  };

  const handleClear = () => {
    setText('');
    toast({
      title: t('toast.cleared'),
      description: t('toast.clearedDescription'),
    });
  };

  // 为不同语言优化示例占位符文本
  const getPlaceholderText = () => {
    if (currentLanguage === 'zh') {
      return t('converter.placeholder');
    } else if (currentLanguage === 'es') {
      return t('converter.placeholder');
    } else if (currentLanguage === 'fr') {
      return t('converter.placeholder');
    } else if (currentLanguage === 'de') {
      return t('converter.placeholder');
    }
    return t('converter.placeholder');
  };

  return (
    <section className="mb-8">
      <h1 className="text-2xl font-bold mb-4 text-center">{t('converter.title')}</h1>
      <p className="mb-2 text-center">{t('converter.introduction')}</p>
      <p className="mb-6 text-center">{t('converter.instructions')}</p>

      <div className="max-w-4xl mx-auto">
        {/* 文本框 */}
        <div className="mb-6">
          <Textarea
            placeholder={getPlaceholderText()}
            className="w-full h-80 p-4 border border-gray-300 rounded-lg text-base"
            value={text}
            onChange={handleTextChange}
          />
        </div>

        {/* 转换类型按钮 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-center">{t('converter.selectCase')}</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'sentence'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('sentence')}
            >
              {t('cases.sentence')}
            </button>
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'upper'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('upper')}
            >
              {t('cases.upper')}
            </button>
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'lower'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('lower')}
            >
              {t('cases.lower')}
            </button>
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'capitalized'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('capitalized')}
            >
              {t('cases.capitalized')}
            </button>
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'title'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('title')}
            >
              {t('cases.title')}
            </button>
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'alternating'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('alternating')}
            >
              {t('cases.alternating')}
            </button>
            <button
              className={`py-3 px-4 rounded-lg text-sm font-medium transition-colors ${
                selectedCase === 'inverse'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              onClick={() => handleCaseChange('inverse')}
            >
              {t('cases.inverse')}
            </button>
          </div>
        </div>

        {/* 转换按钮 */}
        <div className="mb-6 text-center">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-8 rounded-lg text-lg font-semibold transition-colors shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleConvert}
            disabled={!text.trim()}
          >
            {t('actions.convert')}
          </button>
        </div>

        {/* 统计信息 */}
        <div className="mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{textStats.characterCount}</div>
                <div className="text-sm text-gray-600">{t('stats.characterCount')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{textStats.wordCount}</div>
                <div className="text-sm text-gray-600">{t('stats.wordCount')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{textStats.sentenceCount}</div>
                <div className="text-sm text-gray-600">{t('stats.sentenceCount')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{textStats.lineCount}</div>
                <div className="text-sm text-gray-600">{t('stats.lineCount')}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap justify-center gap-3">
          <button
            className="bg-green-600 hover:bg-green-700 text-white py-2 px-6 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleCopy}
            disabled={!text.trim()}
          >
            <span className="material-icons text-sm">content_copy</span>
            {t('actions.copy')}
          </button>
          <button
            className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-6 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleDownload}
            disabled={!text.trim()}
          >
            <span className="material-icons text-sm">download</span>
            {t('actions.download')}
          </button>
          <button
            className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-6 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleClear}
            disabled={!text.trim()}
          >
            <span className="material-icons text-sm">clear</span>
            {t('actions.clear')}
          </button>
        </div>
      </div>
    </section>
  );
};

export default TextConverter;
