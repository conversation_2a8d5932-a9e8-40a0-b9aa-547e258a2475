import React from 'react';
import AdSense from './AdSense';
import MockAd from './MockAd';
import { getAdConfig, shouldShowAds, ADSENSE_CONFIG } from '@/config/ads';

interface AdManagerProps {
  /** 广告位置 */
  position: 'top' | 'bottom' | 'sidebar' | 'content';
  /** 额外的CSS类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

/**
 * 广告管理组件
 * 根据位置自动选择合适的广告配置
 */
const AdManager: React.FC<AdManagerProps> = ({
  position,
  className = '',
  style = {}
}) => {
  // 在开发环境中总是显示模拟广告
  const isDevMode = import.meta.env.MODE === 'development';

  // 调试信息
  console.log(`[AdManager] 渲染广告位: ${position}, 开发模式: ${isDevMode}`);

  // 开发环境直接显示模拟广告
  if (isDevMode) {
    const mockAdConfig = {
      format: position === 'top' || position === 'bottom' ? 'horizontal' : 'rectangle'
    };

    return (
      <div className={`ad-${position} ${className}`} style={{ margin: '10px 0', ...style }}>
        <MockAd
          position={position}
          format={mockAdConfig.format as any}
          className={`${position}-ad`}
        />
      </div>
    );
  }

  // 生产环境的逻辑
  const getAdSlotByPosition = (pos: string) => {
    switch (pos) {
      case 'top':
        return 'TOP_BANNER';
      case 'bottom':
        return 'BOTTOM_BANNER';
      case 'sidebar':
        return 'SIDEBAR';
      case 'content':
        return 'CONTENT_MIDDLE';
      default:
        return null;
    }
  };

  const slotName = getAdSlotByPosition(position);
  if (!slotName) {
    return null;
  }

  const adConfig = getAdConfig(slotName as any);
  if (!adConfig || !shouldShowAds()) {
    return null;
  }

  // 根据位置设置默认样式
  const getDefaultStyleByPosition = (pos: string): React.CSSProperties => {
    switch (pos) {
      case 'top':
        return {
          margin: '10px 0',
          padding: '10px 0',
          borderBottom: '1px solid #e5e7eb'
        };
      case 'bottom':
        return {
          margin: '10px 0',
          padding: '10px 0',
          borderTop: '1px solid #e5e7eb'
        };
      case 'sidebar':
        return {
          margin: '20px 0',
          padding: '15px',
          backgroundColor: '#f9fafb',
          borderRadius: '8px'
        };
      case 'content':
        return {
          margin: '30px 0',
          padding: '20px 0'
        };
      default:
        return {};
    }
  };

  const defaultStyle = getDefaultStyleByPosition(position);
  const combinedStyle = { ...defaultStyle, ...style };

  return (
    <div className={`ad-${position} ${className}`} style={combinedStyle}>
      <AdSense
        slot={adConfig.slot}
        format={adConfig.format}
        responsive={adConfig.responsive}
        className={`${position}-ad`}
      />
    </div>
  );
};

export default AdManager;
