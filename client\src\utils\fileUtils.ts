/**
 * Utility functions for handling file operations
 */

// Download text as a file
export function downloadTextAsFile(text: string, filename: string = 'textcaseconversion-download.txt'): void {
  const element = document.createElement('a');
  const file = new Blob([text], {type: 'text/plain'});
  element.href = URL.createObjectURL(file);
  element.download = filename;
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
}

// Copy text to clipboard
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('Failed to copy text to clipboard', err);
    return false;
  }
}
