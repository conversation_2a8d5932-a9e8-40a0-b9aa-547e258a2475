import fs from 'fs';
import path from 'path';

// 动态导入博客文章数据
async function getBlogPosts() {
  try {
    // 读取博客文章数据文件
    const blogDataPath = path.join(process.cwd(), 'client/src/data/blog-posts.ts');
    const blogData = fs.readFileSync(blogDataPath, 'utf-8');
    
    // 简单解析博客文章数据（提取slug和发布日期）
    const posts = [];
    const slugMatches = blogData.match(/slug:\s*["']([^"']+)["']/g);
    const dateMatches = blogData.match(/publishedAt:\s*["']([^"']+)["']/g);
    
    if (slugMatches && dateMatches) {
      for (let i = 0; i < Math.min(slugMatches.length, dateMatches.length); i++) {
        const slug = slugMatches[i].match(/["']([^"']+)["']/)[1];
        const date = dateMatches[i].match(/["']([^"']+)["']/)[1];
        
        posts.push({
          slug,
          lastmod: date,
          changefreq: 'monthly',
          priority: '0.7'
        });
      }
    }
    
    return posts;
  } catch (error) {
    console.warn('⚠️ 无法读取博客文章数据，使用默认数据:', error.message);
    return [
      {
        slug: 'text-conversion-tips',
        lastmod: '2024-12-15',
        changefreq: 'monthly',
        priority: '0.7'
      }
    ];
  }
}

// 网站配置
const config = {
  siteUrl: 'https://textcase.top',
  outputDir: './dist/public',
  clientPublicDir: './client/public',
  lastmod: new Date().toISOString().split('T')[0],
  languages: ['en', 'es', 'fr', 'de', 'zh'],
  pages: [
    {
      path: '/',
      changefreq: 'weekly',
      priority: '1.0'
    },
    {
      path: '/blog',
      changefreq: 'weekly',
      priority: '0.9'
    },
    {
      path: '/privacy-policy',
      changefreq: 'monthly',
      priority: '0.8'
    },
    {
      path: '/terms-of-service',
      changefreq: 'monthly',
      priority: '0.8'
    }
  ],
  excludePaths: ['/404', '/api/', '/test-blog'],
};

// 确保输出目录存在
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// 生成网站地图
async function generateSitemap() {
  const { siteUrl, outputDir, pages, languages, lastmod } = config;
  const blogPosts = await getBlogPosts();

  ensureDirectoryExists(outputDir);

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`;

  // 为每个页面生成所有语言版本的 URL
  pages.forEach(page => {
    // 为每种语言生成 URL
    languages.forEach(lang => {
      const langPath = lang === 'en' ? '' : `/${lang}`;
      const pagePath = page.path === '/' ? '' : page.path;
      const fullPath = `${langPath}${pagePath}`;
      const fullUrl = `${siteUrl}${fullPath}`;

      sitemap += `  <url>
    <loc>${fullUrl}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${lang === 'en' ? page.priority : (parseFloat(page.priority) * 0.9).toFixed(1)}</priority>
    <!-- 多语言链接 -->
`;

      // 添加所有语言版本的链接
      languages.forEach(l => {
        const lPath = l === 'en' ? '' : `/${l}`;
        sitemap += `    <xhtml:link rel="alternate" hreflang="${l}" href="${siteUrl}${lPath}${pagePath}" />\n`;
      });

      // 添加默认语言链接
      sitemap += `    <xhtml:link rel="alternate" hreflang="x-default" href="${siteUrl}${pagePath}" />
  </url>

`;
    });
  });

  // 为博客文章生成所有语言版本的 URL
  blogPosts.forEach(post => {
    languages.forEach(lang => {
      const langPath = lang === 'en' ? '' : `/${lang}`;
      const postPath = `/blog/${post.slug}`;
      const fullPath = `${langPath}${postPath}`;
      const fullUrl = `${siteUrl}${fullPath}`;

      sitemap += `  <url>
    <loc>${fullUrl}</loc>
    <lastmod>${post.lastmod}</lastmod>
    <changefreq>${post.changefreq}</changefreq>
    <priority>${lang === 'en' ? post.priority : (parseFloat(post.priority) * 0.9).toFixed(1)}</priority>
    <!-- 多语言链接 -->
`;

      // 添加所有语言版本的链接
      languages.forEach(l => {
        const lPath = l === 'en' ? '' : `/${l}`;
        sitemap += `    <xhtml:link rel="alternate" hreflang="${l}" href="${siteUrl}${lPath}${postPath}" />\n`;
      });

      // 添加默认语言链接
      sitemap += `    <xhtml:link rel="alternate" hreflang="x-default" href="${siteUrl}${postPath}" />
  </url>

`;
    });
  });

  sitemap += `</urlset>
`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'sitemap.xml'), sitemap);
  console.log(`✅ 网站地图已生成: ${path.join(outputDir, 'sitemap.xml')}`);
  console.log(`📊 包含 ${pages.length} 个页面和 ${blogPosts.length} 个博客文章，共 ${languages.length} 种语言`);

  // 同时复制到客户端公共目录（用于开发环境）
  fs.writeFileSync(path.join(config.clientPublicDir, 'sitemap.xml'), sitemap);
  console.log(`✅ 网站地图已复制到客户端目录: ${path.join(config.clientPublicDir, 'sitemap.xml')}`);
}

// 生成 robots.txt
function generateRobotsTxt() {
  const { siteUrl, outputDir, excludePaths } = config;

  ensureDirectoryExists(outputDir);

  let robotsTxt = `# Robots.txt for ${siteUrl}
# Generated on ${new Date().toISOString()}

# 允许所有搜索引擎爬虫访问
User-agent: *
Allow: /

# 禁止访问的路径
`;

  // 添加禁止访问的路径
  excludePaths.forEach(path => {
    if (path.startsWith('/')) {
      robotsTxt += `Disallow: ${path}\n`;
    }
  });

  robotsTxt += `
# 特定搜索引擎优化
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# 禁止一些常见的爬虫
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

# 网站地图链接
Sitemap: ${siteUrl}/sitemap.xml

# 主机信息
Host: ${siteUrl.replace('https://', '').replace('http://', '')}
`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已生成: ${path.join(outputDir, 'robots.txt')}`);

  // 同时复制到客户端公共目录（用于开发环境）
  fs.writeFileSync(path.join(config.clientPublicDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已复制到客户端目录: ${path.join(config.clientPublicDir, 'robots.txt')}`);
}

// 执行生成
async function main() {
  try {
    await generateSitemap();
    generateRobotsTxt();
    console.log('✅ 网站地图和 robots.txt 生成完成！');
  } catch (error) {
    console.error('❌ 生成过程中出现错误:', error);
    process.exit(1);
  }
}

main();
