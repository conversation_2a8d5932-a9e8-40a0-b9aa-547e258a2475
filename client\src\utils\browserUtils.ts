/**
 * 浏览器检测和兼容性工具
 */

// 检测是否为移动设备
export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// 检测是否为iOS设备
export const isIOS = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
};

// 检测是否为Android设备
export const isAndroid = (): boolean => {
  return /Android/i.test(navigator.userAgent);
};

// 检测是否为Safari浏览器
export const isSafari = (): boolean => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

// 检测是否为IE浏览器
export const isIE = (): boolean => {
  return /*@cc_on!@*/false || !!(document as any).documentMode;
};

// 检测是否为Edge浏览器
export const isEdge = (): boolean => {
  return !isIE() && !!(window as any).StyleMedia;
};

// 检测是否为Chrome浏览器
export const isChrome = (): boolean => {
  return !!window.chrome && (!!window.chrome.webstore || !!window.chrome.runtime);
};

// 检测是否为Firefox浏览器
export const isFirefox = (): boolean => {
  return typeof (window as any).InstallTrigger !== 'undefined';
};

// 检测是否支持触摸事件
export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// 检测是否支持WebP图片格式
export const supportsWebP = (): Promise<boolean> => {
  return new Promise(resolve => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = 'data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=';
  });
};

// 检测是否支持特定CSS属性
export const supportsCSS = (property: string, value?: string): boolean => {
  if (!window.CSS || !window.CSS.supports) {
    return false;
  }
  
  if (value) {
    return window.CSS.supports(property, value);
  }
  return window.CSS.supports(property);
};

// 检测是否支持Passive事件监听器
export const supportsPassiveEvents = (): boolean => {
  let supportsPassive = false;
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get: function() {
        supportsPassive = true;
        return true;
      }
    });
    window.addEventListener('testPassive', null as any, opts);
    window.removeEventListener('testPassive', null as any, opts);
  } catch (e) {}
  
  return supportsPassive;
};

// 获取浏览器语言
export const getBrowserLanguage = (): string => {
  return (
    navigator.languages && navigator.languages.length
      ? navigator.languages[0]
      : navigator.language
  ).split('-')[0];
};

// 检测是否为暗色模式
export const isDarkMode = (): boolean => {
  return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
};

// 检测是否为减弱动画模式
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// 检测浏览器是否支持某些现代功能
export const checkBrowserFeatures = (): Record<string, boolean> => {
  return {
    flexbox: supportsCSS('display', 'flex'),
    grid: supportsCSS('display', 'grid'),
    customProperties: supportsCSS('--custom-property', 'value'),
    intersectionObserver: 'IntersectionObserver' in window,
    mutationObserver: 'MutationObserver' in window,
    resizeObserver: 'ResizeObserver' in window,
    serviceWorker: 'serviceWorker' in navigator,
    webp: false, // 将由异步函数更新
    webgl: (() => {
      try {
        const canvas = document.createElement('canvas');
        return !!(window.WebGLRenderingContext && 
          (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
      } catch (e) {
        return false;
      }
    })(),
    webrtc: 'RTCPeerConnection' in window,
    webworker: 'Worker' in window,
    fetch: 'fetch' in window,
    promises: 'Promise' in window,
    localstorage: (() => {
      try {
        return 'localStorage' in window && window.localStorage !== null;
      } catch (e) {
        return false;
      }
    })(),
    sessionstorage: (() => {
      try {
        return 'sessionStorage' in window && window.sessionStorage !== null;
      } catch (e) {
        return false;
      }
    })(),
    touchevents: isTouchDevice(),
    passiveEvents: supportsPassiveEvents()
  };
};
