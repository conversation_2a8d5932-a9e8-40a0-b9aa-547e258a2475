import i18next from 'i18next';
import { initReactI18next, I18nextProvider } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import React, { ReactNode } from 'react';

import enTranslation from '../locales/en.json';
import esTranslation from '../locales/es.json';
import frTranslation from '../locales/fr.json';
import deTranslation from '../locales/de.json';
import zhTranslation from '../locales/zh.json';

const resources = {
  en: {
    translation: enTranslation
  },
  es: {
    translation: esTranslation
  },
  fr: {
    translation: frTranslation
  },
  de: {
    translation: deTranslation
  },
  zh: {
    translation: zhTranslation
  }
};

// 为 TypeScript 添加全局声明
declare global {
  interface Window {
    __INITIAL_LANGUAGE__?: string;
    __I18N_INSTANCES__?: Record<string, typeof i18next>;
  }
}

// 存储 SSG 阶段创建的 i18n 实例
const ssgI18nInstances: Record<string, typeof i18next> = {};

// 客户端实例
let clientInstance: typeof i18next | null = null;

// 创建全局实例容器，便于调试
if (typeof window !== 'undefined') {
  window.__I18N_INSTANCES__ = ssgI18nInstances;
}

// 获取初始语言
const getInitialLanguage = (): string => {
  // 在服务器端渲染时，默认使用英语
  if (typeof window === 'undefined') {
    return 'en';
  }

  // 在客户端，检查是否有预设的语言
  if (window.__INITIAL_LANGUAGE__) {
    return window.__INITIAL_LANGUAGE__;
  }

  // 从路径中获取语言（这是最优先的方法，确保与当前URL匹配）
  const pathParts = window.location.pathname.split('/').filter(Boolean);
  if (pathParts.length > 0) {
    const langCode = pathParts[0];
    if (['en', 'es', 'fr', 'de', 'zh'].includes(langCode)) {
      return langCode;
    }
  }

  // 从 localStorage 中获取语言
  try {
    const storedLang = localStorage.getItem('i18nextLng');
    if (storedLang && ['en', 'es', 'fr', 'de', 'zh'].includes(storedLang)) {
      return storedLang;
    }
  } catch (e) {
    console.warn('无法访问 localStorage:', e);
  }

  // 从浏览器语言中获取语言
  try {
    const browserLang = navigator.language.split('-')[0];
    if (['en', 'es', 'fr', 'de', 'zh'].includes(browserLang)) {
      return browserLang;
    }
  } catch (e) {
    console.warn('无法获取浏览器语言:', e);
  }

  return 'en';
};

// 为 SSG 创建一个新的 i18n 实例，每个语言一个
export function createI18nInstanceForSSG(language: string): typeof i18next {
  // 在 SSG 过程中，为每个渲染请求创建全新独立的 i18n 实例
  // 这解决了并发生成时的实例污染问题
  if (typeof window === 'undefined') {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[SSG] 为语言 ${language} 创建全新独立 i18n 实例 (避免污染)`);
    }

    // 在 SSG 环境下，强制创建新实例，避免并发污染
    const instance = i18next.createInstance();

    instance
      .use(initReactI18next)
      .init({
        resources,
        lng: language,
        fallbackLng: 'en',
        interpolation: {
          escapeValue: false
        },
        react: {
          useSuspense: false
        }
      });

    // 强制设置语言，确保初始化后语言正确
    instance.changeLanguage(language);

    // 添加实例标识，方便调试
    (instance as any).__language_id = `ssg_${language}_${Date.now()}`;

    return instance;
  }

  // 检查是否已有此语言的实例
  if (ssgI18nInstances[language]) {
    // 在客户端，可以复用实例
    const instance = ssgI18nInstances[language];

    // 确保实例使用正确的语言
    if (instance.language !== language) {
      if (process.env.NODE_ENV !== 'production') {
        console.warn(`[i18n] 实例语言不匹配，当前是 ${instance.language}，正在切换到 ${language}`);
      }
      instance.changeLanguage(language);
    }

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[客户端] 复用语言 ${language} 的现有 i18n 实例`);
    }

    return instance;
  }

  // 创建新实例
  const instance = i18next.createInstance();

  instance
    .use(initReactI18next)
    .init({
      resources,
      lng: language,
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false
      },
      detection: {
        order: ['path', 'localStorage', 'navigator', 'htmlTag'],
        lookupFromPathIndex: 0,
        caches: ['localStorage']
      },
      react: {
        useSuspense: false
      }
    });

  // 强制设置语言，确保初始化后语言正确
  instance.changeLanguage(language);

  // 添加实例标识，方便调试
  (instance as any).__language_id = `client_${language}_${Date.now()}`;

  // 存储实例 (仅在客户端)
  if (typeof window !== 'undefined') {
    ssgI18nInstances[language] = instance;

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[客户端] 为语言 ${language} 创建新的 i18n 实例并存储`);
    }
  }

  return instance;
}

// 创建或获取客户端 i18n 实例
export function getClientI18nInstance(): typeof i18next {
  if (typeof window === 'undefined') {
    throw new Error('客户端 i18n 实例不能在服务器端调用');
  }

  // 优先使用当前路径匹配的语言实例
  const pathLang = getCurrentPathLanguage();
  if (pathLang && ssgI18nInstances[pathLang]) {
    const instance = ssgI18nInstances[pathLang];

    // 确保实例使用正确的语言
    if (instance.language !== pathLang) {
      instance.changeLanguage(pathLang);
    }

    return instance;
  }

  if (!clientInstance) {
    clientInstance = i18next.createInstance();

    const initialLang = getInitialLanguage();

    clientInstance
      .use(LanguageDetector)
      .use(initReactI18next)
      .init({
        resources,
        lng: initialLang,
        fallbackLng: 'en',
        interpolation: {
          escapeValue: false
        },
        detection: {
          order: ['path', 'localStorage', 'navigator', 'htmlTag'],
          lookupFromPathIndex: 0,
          caches: ['localStorage']
        },
        react: {
          useSuspense: false
        }
      });

    // 添加语言变化监听器
    clientInstance.on('languageChanged', (lng: string) => {
      // 更新 localStorage
      try {
        localStorage.setItem('i18nextLng', lng);
      } catch (e) {
        console.warn('无法访问 localStorage:', e);
      }

      // 更新 HTML 的 lang 属性
      document.documentElement.lang = lng;
    });

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[客户端] 创建默认 i18n 实例，初始语言: ${clientInstance.language}`);
    }
  }

  return clientInstance;
}

// 从当前路径获取语言
function getCurrentPathLanguage(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const pathParts = window.location.pathname.split('/').filter(Boolean);
  if (pathParts.length > 0) {
    const langCode = pathParts[0];
    if (['en', 'es', 'fr', 'de', 'zh'].includes(langCode)) {
      return langCode;
    }
  }

  return null;
}

// 获取特定语言的 i18n 实例（适用于 SSG 和客户端）
export function getI18nInstanceForLanguage(language: string): typeof i18next {
  // SSG环境：始终创建新实例，确保隔离
  if (typeof window === 'undefined') {
    return createI18nInstanceForSSG(language);
  }

  // 客户端：优先复用已有实例
  if (ssgI18nInstances[language]) {
    const instance = ssgI18nInstances[language];
    // 确保语言正确
    if (instance.language !== language) {
      instance.changeLanguage(language);
    }
    return instance;
  }

  // 如果没有，创建新实例
  return createI18nInstanceForSSG(language);
}

// 检查是否有为当前路由创建的专用实例
// 这是在 SSG 过程中由 main.tsx 设置的
function getCurrentRouteInstance(): typeof i18next | null {
  if (typeof window === 'undefined' && (i18next as any).__i18n_instance_for_route) {
    return (i18next as any).__i18n_instance_for_route;
  }
  return null;
}

// 兼容旧代码的默认导出
// 根据环境返回适当的实例
const i18n = typeof window === 'undefined'
  ? getCurrentRouteInstance() || createI18nInstanceForSSG('en') // 优先使用当前路由的专用实例
  : getClientI18nInstance();

export default i18n;
