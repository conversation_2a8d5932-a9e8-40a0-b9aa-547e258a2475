/* 导入兼容性样式 */
@import './styles/compatibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* AdSense 广告样式 */
.adsense-container {
  @apply flex justify-center items-center;
}

.adsense-container .adsbygoogle {
  @apply max-w-full;
}

/* 顶部横幅广告样式 */
.top-banner-ad {
  @apply py-2;
}

/* 底部横幅广告样式 */
.bottom-banner-ad {
  @apply py-2;
}

/* 响应式广告样式 */
@media (max-width: 768px) {
  .adsense-container {
    @apply px-2;
  }

  .top-banner-ad,
  .bottom-banner-ad {
    @apply py-1;
  }
}

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 122 39% 49%;
  --secondary-foreground: 24 9.8% 10%;
  --accent: 36 100% 50%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 20 14.3% 4.1%;
  --radius: 0.5rem;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 122 39% 49%;
  --secondary-foreground: 0 0% 98%;
  --accent: 36 100% 50%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-gray-50 text-text;
  }
}

.btn-case {
  @apply px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md transition-colors;
}

.btn-case.active {
  @apply bg-primary text-white;
}

.btn-action {
  @apply px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors flex items-center;
}

.btn-support {
  @apply flex items-center px-4 py-2 bg-accent text-white rounded-md hover:bg-amber-600 transition-colors;
}

.tool-card {
  @apply p-4 border border-gray-200 rounded-lg hover:border-primary hover:shadow-md transition-all flex items-center;
}

.text-primary {
  color: hsl(var(--primary));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}

.text-secondary {
  color: hsl(var(--secondary));
}

.bg-secondary {
  background-color: hsl(var(--secondary));
}

.text-accent {
  color: hsl(var(--accent));
}

.bg-accent {
  background-color: hsl(var(--accent));
}

.text-text {
  color: #333333;
}
