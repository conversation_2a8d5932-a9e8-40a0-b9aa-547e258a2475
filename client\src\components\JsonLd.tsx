import React from 'react';
import { useTranslation } from 'react-i18next';

interface JsonLdProps {
  type?: 'website' | 'organization' | 'breadcrumb' | 'faq';
  path?: string;
  faqData?: Array<{question: string; answer: string}>;
}

const JsonLd: React.FC<JsonLdProps> = ({ type = 'website', path = '', faqData = [] }) => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const baseUrl = 'https://textcase.top';
  const currentUrl = `${baseUrl}${path}`;

  // 网站基本信息的JSON-LD
  const websiteJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'url': baseUrl,
    'name': t('meta.title'),
    'description': t('meta.description'),
    'potentialAction': {
      '@type': 'SearchAction',
      'target': `${baseUrl}/?q={search_term_string}`,
      'query-input': 'required name=search_term_string'
    },
    'inLanguage': currentLanguage
  };

  // 组织信息的JSON-LD
  const organizationJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    'url': baseUrl,
    'name': 'Text Case Conversion',
    'logo': `${baseUrl}/logo.png`,
    'sameAs': [
      'https://twitter.com/textcaseconversion',
      'https://github.com/textcaseconversion'
    ]
  };

  // 面包屑导航的JSON-LD
  const breadcrumbJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    'itemListElement': [
      {
        '@type': 'ListItem',
        'position': 1,
        'name': 'Home',
        'item': baseUrl
      },
      {
        '@type': 'ListItem',
        'position': 2,
        'name': path.startsWith('/privacy-policy') ? 'Privacy Policy' : 'Terms of Service',
        'item': currentUrl
      }
    ]
  };

  // FAQ页面的JSON-LD
  const faqJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    'mainEntity': faqData.map(item => ({
      '@type': 'Question',
      'name': item.question,
      'acceptedAnswer': {
        '@type': 'Answer',
        'text': item.answer
      }
    }))
  };

  // 根据类型选择适当的JSON-LD
  const getJsonLdByType = () => {
    switch (type) {
      case 'organization':
        return organizationJsonLd;
      case 'breadcrumb':
        return breadcrumbJsonLd;
      case 'faq':
        return faqJsonLd;
      case 'website':
      default:
        return websiteJsonLd;
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(getJsonLdByType()) }}
    />
  );
};

export default JsonLd;
