import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
// 不再需要引入 i18n 实例工厂函数，因为我们在 SSGWrapper 中处理了

export default defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets"),
    },
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
  },
  ssgOptions: {
    script: 'async',
    formatting: 'none',
    dirStyle: 'nested',
    includedRoutes: (_paths) => {
      const langs = ['en', 'es', 'fr', 'de', 'zh']
      const basePaths = ['/', '/privacy-policy', '/terms-of-service']
      return langs.flatMap(lang => {
        // 为英文添加默认路由和 /en 路由
        if (lang === 'en') {
          return [
            ...basePaths, // 默认路由: /, /privacy-policy, /terms-of-service
            ...basePaths.map(path => `/en${path === '/' ? '' : path}`) // /en 路由: /en, /en/privacy-policy, /en/terms-of-service
          ]
        }
        // 其他语言只添加带前缀的路由
        return basePaths.map(path => `/${lang}${path === '/' ? '' : path}`)
      })
    },
    onPageRendered: (route, html) => {
      // 根据路由路径设置语言
      type SupportedLanguage = 'en' | 'es' | 'fr' | 'de' | 'zh';
      let lang: SupportedLanguage = 'en';
      if (route.startsWith('/en')) {
        lang = 'en';
      } else if (route.startsWith('/es')) {
        lang = 'es';
      } else if (route.startsWith('/fr')) {
        lang = 'fr';
      } else if (route.startsWith('/de')) {
        lang = 'de';
      } else if (route.startsWith('/zh')) {
        lang = 'zh';
      }

      console.log(`Rendering page for route: ${route}, language: ${lang}`);

      // 添加 hreflang 和 canonical 标签
      const domain = 'https://textcase.top';
      const currentPath = route.replace(/\/$/, ''); // 移除末尾的斜杠
      const pagePath = currentPath.split('/').slice(2).join('/'); // 获取页面路径（不包括语言代码）

      // 构建当前页面的规范 URL
      let canonicalUrl = '';
      if (lang === 'en') {
        canonicalUrl = pagePath ? `${domain}/${pagePath}/` : `${domain}/`;
      } else {
        canonicalUrl = pagePath ? `${domain}/${lang}/${pagePath}/` : `${domain}/${lang}/`;
      }

      // 构建所有语言版本的 URL
      const alternateUrls = {
        en: pagePath ? `${domain}/${pagePath}/` : `${domain}/`,
        es: pagePath ? `${domain}/es/${pagePath}/` : `${domain}/es/`,
        fr: pagePath ? `${domain}/fr/${pagePath}/` : `${domain}/fr/`,
        de: pagePath ? `${domain}/de/${pagePath}/` : `${domain}/de/`,
        zh: pagePath ? `${domain}/zh/${pagePath}/` : `${domain}/zh/`
      };

      // 移除已有的 canonical 和 hreflang 标签
      html = html.replace(/<link[^>]*rel="canonical"[^>]*>/g, '');
      html = html.replace(/<link[^>]*rel="alternate"[^>]*hreflang="[^"]*"[^>]*>/g, '');

      // 添加 canonical 和 hreflang 标签
      let linkTags = `<link rel="canonical" href="${canonicalUrl}" />`;
      for (const [hrefLang, url] of Object.entries(alternateUrls)) {
        linkTags += `\n  <link rel="alternate" hreflang="${hrefLang}" href="${url}" />`;
      }
      // 添加 x-default hreflang
      linkTags += `\n  <link rel="alternate" hreflang="x-default" href="${alternateUrls.en}" />`;

      // 插入标签到 head 中
      html = html.replace('</head>', `${linkTags}\n</head>`);

      // 定义每种语言的关键UI文本
      const translations: Record<SupportedLanguage, {
        title: string;
        description: string;
      }> = {
        en: {
          title: "Text Case Converter - Modern Text Case Conversion Tool",
          description: "Text Case Converter is a powerful text conversion tool that helps you transform text to uppercase, lowercase, title case and more. Supports multiple languages."
        },
        es: {
          title: "Text Case Converter - Herramienta Moderna de Conversión de Casos de Texto",
          description: "Text Case Converter es una potente herramienta de conversión de texto que te ayuda a transformar texto a mayúsculas, minúsculas, título y más. Compatible con varios idiomas."
        },
        fr: {
          title: "Text Case Converter - Outil Moderne de Conversion de Casse de Texte",
          description: "Text Case Converter est un puissant outil de conversion de texte qui vous aide à transformer le texte en majuscules, minuscules, casse de titre et plus encore. Prend en charge plusieurs langues."
        },
        de: {
          title: "Text Case Converter - Modernes Text-Umwandlungstool",
          description: "Text Case Converter ist ein leistungsstarkes Text-Umwandlungstool, das Ihnen hilft, Text in Großbuchstaben, Kleinbuchstaben, Titelfall und mehr umzuwandeln. Unterstützt mehrere Sprachen."
        },
        zh: {
          title: "Text Case Converter - 现代文本大小写转换工具",
          description: "Text Case Converter 是一个强大的文本转换工具，可帮助您将文本转换为大写、小写、标题大小写等。支持多种语言。"
        }
      };

      // 替换 title 标签
      html = html.replace(/<title[^>]*>(.*?)<\/title>/g, `<title>${translations[lang].title}</title>`);

      // 替换 meta description 标签
      html = html.replace(/<meta[^>]*name="description"[^>]*content="([^"]*)"[^>]*>/g,
        `<meta name="description" content="${translations[lang].description}">`);

      // 替换 Open Graph 标签
      html = html.replace(/<meta[^>]*property="og:title"[^>]*content="([^"]*)"[^>]*>/g,
        `<meta property="og:title" content="${translations[lang].title}">`);

      html = html.replace(/<meta[^>]*property="og:description"[^>]*content="([^"]*)"[^>]*>/g,
        `<meta property="og:description" content="${translations[lang].description}">`);

      // 设置正确的 og:locale
      html = html.replace(/<meta[^>]*property="og:locale"[^>]*content="([^"]*)"[^>]*>/g,
        `<meta property="og:locale" content="${lang}">`);

      // 替换 Twitter 卡片标签
      html = html.replace(/<meta[^>]*name="twitter:title"[^>]*content="([^"]*)"[^>]*>/g,
        `<meta name="twitter:title" content="${translations[lang].title}">`);

      html = html.replace(/<meta[^>]*name="twitter:description"[^>]*content="([^"]*)"[^>]*>/g,
        `<meta name="twitter:description" content="${translations[lang].description}">`);

      // 设置正确的 html lang 属性
      html = html.replace(/<html[^>]*lang="([^"]*)"[^>]*>/g, `<html lang="${lang}">`);

      // 强制替换 data-rh 属性的元数据
      html = html.replace(/<title data-rh="true">(.*?)<\/title>/g,
        `<title data-rh="true">${translations[lang].title}</title>`);

      html = html.replace(/<meta data-rh="true" name="description" content="([^"]*)"/g,
        `<meta data-rh="true" name="description" content="${translations[lang].description}"`);

      html = html.replace(/<meta data-rh="true" property="og:title" content="([^"]*)"/g,
        `<meta data-rh="true" property="og:title" content="${translations[lang].title}"`);

      html = html.replace(/<meta data-rh="true" property="og:description" content="([^"]*)"/g,
        `<meta data-rh="true" property="og:description" content="${translations[lang].description}"`);

      html = html.replace(/<meta data-rh="true" property="og:locale" content="([^"]*)"/g,
        `<meta data-rh="true" property="og:locale" content="${lang}"`);

      html = html.replace(/<meta data-rh="true" name="twitter:title" content="([^"]*)"/g,
        `<meta data-rh="true" name="twitter:title" content="${translations[lang].title}"`);

      html = html.replace(/<meta data-rh="true" name="twitter:description" content="([^"]*)"/g,
        `<meta data-rh="true" name="twitter:description" content="${translations[lang].description}"`);

      // 修正 og:url 属性
      html = html.replace(/<meta data-rh="true" property="og:url" content="([^"]*)"/g,
        `<meta data-rh="true" property="og:url" content="${canonicalUrl}"`);

      // 预定义的翻译数据，避免动态导入
      const translationData = {
        en: {
          "converter": {
            "title": "Text Case Converter Tool",
            "introduction": "Accidentally left caps lock on and typed something, but can't be bothered to start again and retype it all?",
            "instructions": "Simply enter your text and choose the case you want to convert it to.",
            "placeholder": "Type or paste your content here..."
          },
          "cases": {
            "sentence": "Sentence case",
            "lower": "lowercase",
            "upper": "UPPERCASE",
            "capitalized": "Capitalized Case",
            "alternating": "aLtErNaTiNg cAsE",
            "title": "Title Case",
            "inverse": "InVeRsE CaSe"
          },
          "actions": {
            "download": "Download",
            "copy": "Copy",
            "clear": "Clear",
            "support": "Support this project"
          },
          "language": {
            "selectLanguage": "Language"
          },
          "stats": {
            "characterCount": "Characters",
            "wordCount": "Words",
            "sentenceCount": "Sentences",
            "lineCount": "Lines"
          },
          "explanations": {
            "sectionTitle": "About Text Case Conversion",
            "sentence": {
              "title": "Sentence Case",
              "description1": "Sentence case is the standard format for writing in English. It capitalizes the first letter of the first word in a sentence and proper nouns.",
              "description2": "This is the most common format for general writing, emails, and most content.",
              "example": "Example: This is a sentence. Another sentence follows."
            },
            "lower": {
              "title": "lowercase",
              "description": "Lowercase formatting removes all capitalization from your text, making every letter lowercase.",
              "example": "Example: this is all lowercase text."
            },
            "upper": {
              "title": "UPPERCASE",
              "description": "Uppercase formatting capitalizes every letter in your text. It's often used for emphasis or headings.",
              "example": "Example: THIS IS ALL UPPERCASE TEXT."
            },
            "capitalized": {
              "title": "Capitalized Case",
              "description": "Capitalized case (or Start Case) capitalizes the first letter of every word in your text.",
              "example": "Example: This Is Capitalized Case Text."
            },
            "alternating": {
              "title": "aLtErNaTiNg cAsE",
              "description": "Alternating case switches between lowercase and uppercase letters throughout your text.",
              "example": "Example: tHiS iS aLtErNaTiNg CaSe TeXt."
            },
            "title": {
              "title": "Title Case",
              "description": "Title case capitalizes the first letter of each major word (nouns, verbs, adjectives) while leaving articles, conjunctions, and prepositions lowercase unless they're the first word.",
              "example": "Example: This Is a Title Case Example."
            }
          },
          "faq": {
            "title": "Frequently Asked Questions",
            "why": {
              "question": "Why would I need a text case converter?",
              "answer": "Text case converters are useful in many situations: fixing text that was accidentally typed with caps lock on, preparing titles for publications, creating consistent formatting for code or documentation, or simply for stylistic purposes in design and social media."
            },
            "secure": {
              "question": "Is my text secure when using this tool?",
              "answer": "Yes! This tool processes all text directly in your browser. Your content never leaves your device or gets sent to any server, ensuring complete privacy and security."
            },
            "mobile": {
              "question": "Does this work on mobile devices?",
              "answer": "Absolutely! This text case converter is fully responsive and works on smartphones, tablets, and desktop computers."
            },
            "languages": {
              "question": "What languages are supported?",
              "answer": "The converter works with any language that has case distinctions. While it's optimized for English, it handles most Latin-based languages well. For languages without case distinctions (like Chinese or Japanese), only some conversions will apply."
            }
          }
        },
        es: {
          "converter": {
            "title": "Herramienta de Conversión de Casos de Texto",
            "introduction": "¿Accidentalmente dejaste el bloq mayús activado y escribiste algo, pero no quieres volver a empezar y reescribirlo todo?",
            "instructions": "Simplemente ingresa tu texto y elige el caso al que deseas convertirlo.",
            "placeholder": "Escribe o pega tu contenido aquí..."
          },
          "cases": {
            "sentence": "Caso de oración",
            "lower": "minúsculas",
            "upper": "MAYÚSCULAS",
            "capitalized": "Caso Capitalizado",
            "alternating": "cAsO AlTeRnAdO",
            "title": "Caso Título",
            "inverse": "CaSo InVeRsO"
          },
          "actions": {
            "download": "Descargar",
            "copy": "Copiar",
            "clear": "Limpiar",
            "support": "Apoyar este proyecto"
          },
          "language": {
            "selectLanguage": "Idioma"
          },
          "stats": {
            "characterCount": "Caracteres",
            "wordCount": "Palabras",
            "sentenceCount": "Oraciones",
            "lineCount": "Líneas"
          },
          "explanations": {
            "sectionTitle": "Acerca de la Conversión de Casos de Texto",
            "sentence": {
              "title": "Caso de Oración",
              "description1": "El caso de oración es el formato estándar para escribir en español. Capitaliza la primera letra de la primera palabra en una oración y los nombres propios.",
              "description2": "Este es el formato más común para escritura general, correos electrónicos y la mayoría del contenido.",
              "example": "Ejemplo: Esta es una oración. Otra oración le sigue."
            },
            "lower": {
              "title": "minúsculas",
              "description": "El formato en minúsculas elimina todas las mayúsculas de tu texto, convirtiendo cada letra en minúscula.",
              "example": "Ejemplo: este es un texto todo en minúsculas."
            },
            "upper": {
              "title": "MAYÚSCULAS",
              "description": "El formato en mayúsculas capitaliza cada letra en tu texto. Se usa a menudo para énfasis o encabezados.",
              "example": "Ejemplo: ESTE ES UN TEXTO TODO EN MAYÚSCULAS."
            },
            "capitalized": {
              "title": "Caso Capitalizado",
              "description": "El caso capitalizado (o caso inicial) capitaliza la primera letra de cada palabra en tu texto.",
              "example": "Ejemplo: Este Es Un Texto En Caso Capitalizado."
            },
            "alternating": {
              "title": "cAsO AlTeRnAdO",
              "description": "El caso alternado alterna entre letras minúsculas y mayúsculas a lo largo de tu texto.",
              "example": "Ejemplo: eStE eS uN tExTo En CaSo AlTeRnAdO."
            },
            "title": {
              "title": "Caso Título",
              "description": "El caso título capitaliza la primera letra de cada palabra principal (sustantivos, verbos, adjetivos) mientras deja artículos, conjunciones y preposiciones en minúsculas a menos que sean la primera palabra.",
              "example": "Ejemplo: Este es un Ejemplo de Caso Título."
            }
          },
          "faq": {
            "title": "Preguntas Frecuentes",
            "why": {
              "question": "¿Por qué necesitaría un convertidor de casos de texto?",
              "answer": "Los convertidores de casos de texto son útiles en muchas situaciones: arreglar texto que fue escrito accidentalmente con el bloq mayús activado, preparar títulos para publicaciones, crear formato consistente para código o documentación, o simplemente por propósitos estilísticos en diseño y redes sociales."
            },
            "secure": {
              "question": "¿Mi texto está seguro al usar esta herramienta?",
              "answer": "¡Sí! Esta herramienta procesa todo el texto directamente en tu navegador. Tu contenido nunca sale de tu dispositivo ni se envía a ningún servidor, asegurando completa privacidad y seguridad."
            },
            "mobile": {
              "question": "¿Funciona en dispositivos móviles?",
              "answer": "¡Absolutamente! Este convertidor de casos de texto es completamente responsivo y funciona en smartphones, tablets y computadoras de escritorio."
            },
            "languages": {
              "question": "¿Qué idiomas son compatibles?",
              "answer": "El convertidor funciona con cualquier idioma que tenga distinciones de caso. Aunque está optimizado para español, maneja bien la mayoría de los idiomas basados en latín. Para idiomas sin distinciones de caso (como chino o japonés), solo algunas conversiones se aplicarán."
            }
          }
        },
        fr: {
          "converter": {
            "title": "Outil de Conversion de Casse de Texte",
            "introduction": "Vous avez accidentellement laissé le verrouillage des majuscules activé et tapé quelque chose, mais vous ne voulez pas recommencer et tout retaper?",
            "instructions": "Entrez simplement votre texte et choisissez la casse à laquelle vous souhaitez le convertir.",
            "placeholder": "Tapez ou collez votre contenu ici..."
          },
          "cases": {
            "sentence": "Casse de phrase",
            "lower": "minuscules",
            "upper": "MAJUSCULES",
            "capitalized": "Casse Capitalisée",
            "alternating": "cAsSe AlTeRnÉe",
            "title": "Casse de Titre",
            "inverse": "CaSsE InVeRsÉe"
          },
          "actions": {
            "download": "Télécharger",
            "copy": "Copier",
            "clear": "Effacer",
            "support": "Soutenir ce projet"
          },
          "language": {
            "selectLanguage": "Langue"
          },
          "stats": {
            "characterCount": "Caractères",
            "wordCount": "Mots",
            "sentenceCount": "Phrases",
            "lineCount": "Lignes"
          },
          "explanations": {
            "sectionTitle": "À propos de la Conversion de Casse de Texte",
            "sentence": {
              "title": "Casse de Phrase",
              "description1": "La casse de phrase est le format standard pour écrire en français. Elle met en majuscule la première lettre du premier mot d'une phrase et les noms propres.",
              "description2": "C'est le format le plus courant pour l'écriture générale, les emails et la plupart des contenus.",
              "example": "Exemple: Ceci est une phrase. Une autre phrase suit."
            },
            "lower": {
              "title": "minuscules",
              "description": "Le format en minuscules supprime toutes les majuscules de votre texte, rendant chaque lettre minuscule.",
              "example": "Exemple: ceci est un texte tout en minuscules."
            },
            "upper": {
              "title": "MAJUSCULES",
              "description": "Le format en majuscules met en capitale chaque lettre de votre texte. Il est souvent utilisé pour l'emphase ou les titres.",
              "example": "Exemple: CECI EST UN TEXTE TOUT EN MAJUSCULES."
            },
            "capitalized": {
              "title": "Casse Capitalisée",
              "description": "La casse capitalisée (ou casse initiale) met en majuscule la première lettre de chaque mot dans votre texte.",
              "example": "Exemple: Ceci Est Un Texte En Casse Capitalisée."
            },
            "alternating": {
              "title": "cAsSe AlTeRnÉe",
              "description": "La casse alternée alterne entre lettres minuscules et majuscules tout au long de votre texte.",
              "example": "Exemple: cEcI eSt Un TeXtE eN cAsSe AlTeRnÉe."
            },
            "title": {
              "title": "Casse de Titre",
              "description": "La casse de titre met en majuscule la première lettre de chaque mot principal (noms, verbes, adjectifs) tout en laissant les articles, conjonctions et prépositions en minuscules sauf s'ils sont le premier mot.",
              "example": "Exemple: Ceci est un Exemple de Casse de Titre."
            }
          },
          "faq": {
            "title": "Questions Fréquemment Posées",
            "why": {
              "question": "Pourquoi aurais-je besoin d'un convertisseur de casse de texte?",
              "answer": "Les convertisseurs de casse de texte sont utiles dans de nombreuses situations: corriger un texte qui a été accidentellement tapé avec le verrouillage des majuscules activé, préparer des titres pour des publications, créer un formatage cohérent pour du code ou de la documentation, ou simplement pour des raisons stylistiques en design et sur les réseaux sociaux."
            },
            "secure": {
              "question": "Mon texte est-il sécurisé lors de l'utilisation de cet outil?",
              "answer": "Oui! Cet outil traite tout le texte directement dans votre navigateur. Votre contenu ne quitte jamais votre appareil et n'est envoyé à aucun serveur, assurant une confidentialité et une sécurité complètes."
            },
            "mobile": {
              "question": "Cela fonctionne-t-il sur les appareils mobiles?",
              "answer": "Absolument! Ce convertisseur de casse de texte est entièrement responsive et fonctionne sur smartphones, tablettes et ordinateurs de bureau."
            },
            "languages": {
              "question": "Quelles langues sont prises en charge?",
              "answer": "Le convertisseur fonctionne avec n'importe quelle langue qui a des distinctions de casse. Bien qu'il soit optimisé pour le français, il gère bien la plupart des langues basées sur le latin. Pour les langues sans distinctions de casse (comme le chinois ou le japonais), seules certaines conversions s'appliqueront."
            }
          }
        },
        de: {
          "converter": {
            "title": "Text-Umwandlungstool",
            "introduction": "Haben Sie versehentlich die Feststelltaste aktiviert gelassen und etwas getippt, möchten aber nicht von vorne beginnen und alles neu tippen?",
            "instructions": "Geben Sie einfach Ihren Text ein und wählen Sie die Schreibweise, in die Sie ihn umwandeln möchten.",
            "placeholder": "Tippen oder fügen Sie Ihren Inhalt hier ein..."
          },
          "cases": {
            "sentence": "Satzschreibweise",
            "lower": "kleinbuchstaben",
            "upper": "GROSSBUCHSTABEN",
            "capitalized": "Großgeschriebene Schreibweise",
            "alternating": "wEcHsElNdE ScHrEiBwEiSe",
            "title": "Titelschreibweise",
            "inverse": "InVeRsE ScHrEiBwEiSe"
          },
          "actions": {
            "download": "Herunterladen",
            "copy": "Kopieren",
            "clear": "Löschen",
            "support": "Dieses Projekt unterstützen"
          },
          "language": {
            "selectLanguage": "Sprache"
          },
          "stats": {
            "characterCount": "Zeichen",
            "wordCount": "Wörter",
            "sentenceCount": "Sätze",
            "lineCount": "Zeilen"
          },
          "explanations": {
            "sectionTitle": "Über die Textumwandlung",
            "sentence": {
              "title": "Satzschreibweise",
              "description1": "Die Satzschreibweise ist das Standardformat für das Schreiben auf Deutsch. Sie schreibt den ersten Buchstaben des ersten Wortes in einem Satz und Eigennamen groß.",
              "description2": "Dies ist das gebräuchlichste Format für allgemeines Schreiben, E-Mails und die meisten Inhalte.",
              "example": "Beispiel: Dies ist ein Satz. Ein weiterer Satz folgt."
            },
            "lower": {
              "title": "kleinbuchstaben",
              "description": "Die Kleinschreibung entfernt alle Großbuchstaben aus Ihrem Text und macht jeden Buchstaben klein.",
              "example": "Beispiel: dies ist ein text in kleinbuchstaben."
            },
            "upper": {
              "title": "GROSSBUCHSTABEN",
              "description": "Die Großschreibung schreibt jeden Buchstaben in Ihrem Text groß. Sie wird oft für Betonung oder Überschriften verwendet.",
              "example": "Beispiel: DIES IST EIN TEXT IN GROSSBUCHSTABEN."
            },
            "capitalized": {
              "title": "Großgeschriebene Schreibweise",
              "description": "Die großgeschriebene Schreibweise (oder Anfangsschreibweise) schreibt den ersten Buchstaben jedes Wortes in Ihrem Text groß.",
              "example": "Beispiel: Dies Ist Ein Text In Großgeschriebener Schreibweise."
            },
            "alternating": {
              "title": "wEcHsElNdE ScHrEiBwEiSe",
              "description": "Die wechselnde Schreibweise wechselt zwischen Klein- und Großbuchstaben in Ihrem gesamten Text.",
              "example": "Beispiel: dIeS iSt EiN tExT iN wEcHsElNdEr ScHrEiBwEiSe."
            },
            "title": {
              "title": "Titelschreibweise",
              "description": "Die Titelschreibweise schreibt den ersten Buchstaben jedes Hauptwortes (Substantive, Verben, Adjektive) groß, während Artikel, Konjunktionen und Präpositionen klein geschrieben werden, es sei denn, sie sind das erste Wort.",
              "example": "Beispiel: Dies ist ein Beispiel für Titelschreibweise."
            }
          },
          "faq": {
            "title": "Häufig gestellte Fragen",
            "why": {
              "question": "Warum sollte ich einen Text-Umwandler benötigen?",
              "answer": "Text-Umwandler sind in vielen Situationen nützlich: Korrektur von Text, der versehentlich mit aktivierter Feststelltaste getippt wurde, Vorbereitung von Titeln für Publikationen, Erstellung einheitlicher Formatierung für Code oder Dokumentation oder einfach für stilistische Zwecke in Design und sozialen Medien."
            },
            "secure": {
              "question": "Ist mein Text bei der Verwendung dieses Tools sicher?",
              "answer": "Ja! Dieses Tool verarbeitet den gesamten Text direkt in Ihrem Browser. Ihre Inhalte verlassen niemals Ihr Gerät und werden an keinen Server gesendet, was vollständige Privatsphäre und Sicherheit gewährleistet."
            },
            "mobile": {
              "question": "Funktioniert es auf mobilen Geräten?",
              "answer": "Absolut! Dieser Text-Umwandler ist vollständig responsiv und funktioniert auf Smartphones, Tablets und Desktop-Computern."
            },
            "languages": {
              "question": "Welche Sprachen werden unterstützt?",
              "answer": "Der Umwandler funktioniert mit jeder Sprache, die Unterschiede in der Schreibweise hat. Obwohl er für Deutsch optimiert ist, verarbeitet er die meisten lateinbasierten Sprachen gut. Für Sprachen ohne Unterschiede in der Schreibweise (wie Chinesisch oder Japanisch) gelten nur einige Umwandlungen."
            }
          }
        },
        zh: {
          "converter": {
            "title": "文本大小写转换工具",
            "introduction": "不小心开启了大写锁定并输入了一些内容，但懒得重新开始并重新输入所有内容？",
            "instructions": "只需输入您的文本，然后选择要转换的大小写类型。",
            "placeholder": "在此处键入或粘贴您的内容..."
          },
          "cases": {
            "sentence": "句子大小写",
            "lower": "小写",
            "upper": "大写",
            "capitalized": "首字母大写",
            "alternating": "交替大小写",
            "title": "标题大小写",
            "inverse": "反转大小写"
          },
          "actions": {
            "download": "下载",
            "copy": "复制",
            "clear": "清除",
            "support": "支持此项目"
          },
          "language": {
            "selectLanguage": "语言"
          },
          "stats": {
            "characterCount": "字符",
            "wordCount": "单词",
            "sentenceCount": "句子",
            "lineCount": "行数"
          },
          "explanations": {
            "sectionTitle": "关于文本大小写转换",
            "sentence": {
              "title": "句子大小写",
              "description1": "句子大小写是英文写作的标准格式。它将句子中第一个单词的首字母和专有名词大写。",
              "description2": "这是一般写作、电子邮件和大多数内容最常用的格式。",
              "example": "示例：这是一个句子。另一个句子跟随。"
            },
            "lower": {
              "title": "小写",
              "description": "小写格式移除文本中所有大写字母，使每个字母都变成小写。",
              "example": "示例：这是全部小写的文本。"
            },
            "upper": {
              "title": "大写",
              "description": "大写格式将文本中的每个字母都变成大写。它通常用于强调或标题。",
              "example": "示例：这是全部大写的文本。"
            },
            "capitalized": {
              "title": "首字母大写",
              "description": "首字母大写（或开头大写）将文本中每个单词的首字母大写。",
              "example": "示例：这是首字母大写的文本。"
            },
            "alternating": {
              "title": "交替大小写",
              "description": "交替大小写在整个文本中交替使用小写和大写字母。",
              "example": "示例：这是交替大小写的文本。"
            },
            "title": {
              "title": "标题大小写",
              "description": "标题大小写将每个主要单词（名词、动词、形容词）的首字母大写，而将冠词、连词和介词小写，除非它们是第一个单词。",
              "example": "示例：这是一个标题大小写的示例。"
            }
          },
          "faq": {
            "title": "常见问题",
            "why": {
              "question": "为什么我需要文本大小写转换器？",
              "answer": "文本大小写转换器在许多情况下都很有用：修复意外使用大写锁定键输入的文本，为出版物准备标题，为代码或文档创建一致的格式，或者仅仅是为了设计和社交媒体中的风格目的。"
            },
            "secure": {
              "question": "使用此工具时我的文本安全吗？",
              "answer": "是的！此工具直接在您的浏览器中处理所有文本。您的内容永远不会离开您的设备或发送到任何服务器，确保完全的隐私和安全。"
            },
            "mobile": {
              "question": "它在移动设备上工作吗？",
              "answer": "当然！这个文本大小写转换器完全响应式，可在智能手机、平板电脑和台式电脑上使用。"
            },
            "languages": {
              "question": "支持哪些语言？",
              "answer": "转换器适用于任何有大小写区分的语言。虽然它针对英语进行了优化，但它能很好地处理大多数基于拉丁文的语言。对于没有大小写区分的语言（如中文或日语），只有部分转换会适用。"
            }
          }
        }
      }[lang];

      // 递归替换翻译键
      const replaceTranslationKeys = (obj: any, prefix = '') => {
        for (const key in obj) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          const value = obj[key];

          if (typeof value === 'string') {
            // 替换翻译键
            const regex = new RegExp(`>${fullKey}<`, 'g');
            html = html.replace(regex, `>${value}<`);

            // 替换占位符
            const placeholderRegex = new RegExp(`placeholder="${fullKey}"`, 'g');
            html = html.replace(placeholderRegex, `placeholder="${value}"`);
          } else if (typeof value === 'object' && value !== null) {
            // 递归处理嵌套对象
            replaceTranslationKeys(value, fullKey);
          }
        }
      };

      // 替换页面中的翻译键
      replaceTranslationKeys(translationData);

      // 修复语言选择器和链接
      // 1. 修复主页链接
      html = html.replace(/<a href="\/[a-z]{2}\/"><span/g, `<a href="/${lang}/"><span`);

      // 2. 修复语言选择器中的选中语言
      html = html.replace(/<option value="[a-z]{2}" selected="">/g, `<option value="${lang}" selected="">`);

      // 3. 修复页面中的其他语言链接
      const languagePaths = {
        en: '/',
        es: '/es/',
        fr: '/fr/',
        de: '/de/',
        zh: '/zh/'
      };

      // 替换所有语言链接
      Object.entries(languagePaths).forEach(([langCode, path]) => {
        // 替换语言选择器中的链接
        const optionRegex = new RegExp(`<option value="${langCode}"[^>]*>`, 'g');
        html = html.replace(optionRegex, `<option value="${langCode}"${langCode === lang ? ' selected=""' : ''}>`);

        // 替换导航栏中的链接
        const navLinkRegex = new RegExp(`<a href="${path}"[^>]*>`, 'g');
        html = html.replace(navLinkRegex, `<a href="${path}"${langCode === lang ? ' aria-current="page"' : ''}>`);
      });

      // 修复中文选项
      html = html.replace(/<option value="[a-z]{2}" selected="">中文<\/option>/g,
        `<option value="zh"${lang === 'zh' ? ' selected=""' : ''}>中文</option>`);

      // 4. 修复页面中的文本占位符
      html = html.replace(/placeholder="[^"]*"/g, `placeholder="${translationData.converter.placeholder}"`);

      // 5. 修复按钮文本
      const buttonTexts = {
        'Sentence case': translationData.cases.sentence,
        'lowercase': translationData.cases.lower,
        'UPPERCASE': translationData.cases.upper,
        'Capitalized Case': translationData.cases.capitalized,
        'aLtErNaTiNg cAsE': translationData.cases.alternating,
        'Title Case': translationData.cases.title,
        'InVeRsE CaSe': translationData.cases.inverse,
        'Download': translationData.actions.download,
        'Copy': translationData.actions.copy,
        'Clear': translationData.actions.clear,
        'Support this project': translationData.actions.support,
        'Language': translationData.language.selectLanguage
      };

      // 替换按钮文本
      Object.entries(buttonTexts).forEach(([english, translated]) => {
        if (!translated) return;

        // 替换按钮文本
        const buttonRegex = new RegExp(`>${english}<`, 'g');
        html = html.replace(buttonRegex, `>${translated}<`);
      });

      // 6. 修复页面标题
      html = html.replace(/<h2[^>]*class="[^"]*converter-title[^"]*"[^>]*>[^<]*<\/h2>/g,
        `<h2 class="text-2xl font-bold mb-4 converter-title">${translationData.converter.title}</h2>`);

      // 7. 修复页面介绍
      html = html.replace(/<p[^>]*class="[^"]*converter-intro[^"]*"[^>]*>[^<]*<\/p>/g,
        `<p class="text-gray-600 mb-4 converter-intro">${translationData.converter.introduction}</p>`);

      // 8. 修复页面说明
      html = html.replace(/<p[^>]*class="[^"]*converter-instructions[^"]*"[^>]*>[^<]*<\/p>/g,
        `<p class="text-gray-600 mb-6 converter-instructions">${translationData.converter.instructions}</p>`);

      // 9. 修复页脚
      // 替换中文页脚为对应语言的页脚
      const footerTexts = {
        en: {
          resources: "Resources",
          privacyPolicy: "Privacy Policy",
          termsOfService: "Terms of Service",
          copyright: "© 2025 Text Case Converter. All rights reserved."
        },
        es: {
          resources: "Recursos",
          privacyPolicy: "Política de Privacidad",
          termsOfService: "Términos de Servicio",
          copyright: "© 2025 Text Case Converter. Todos los derechos reservados."
        },
        fr: {
          resources: "Ressources",
          privacyPolicy: "Politique de Confidentialité",
          termsOfService: "Conditions d'Utilisation",
          copyright: "© 2025 Text Case Converter. Tous droits réservés."
        },
        de: {
          resources: "Ressourcen",
          privacyPolicy: "Datenschutzrichtlinie",
          termsOfService: "Nutzungsbedingungen",
          copyright: "© 2025 Text Case Converter. Alle Rechte vorbehalten."
        },
        zh: {
          resources: "资源",
          privacyPolicy: "隐私政策",
          termsOfService: "服务条款",
          copyright: "© 2025 Text Case Converter. 保留所有权利。"
        }
      };

      // 替换资源标题（仅在页脚部分）
      html = html.replace(/<div class="md:text-right"><h3[^>]*class="[^"]*font-semibold mb-2[^"]*"[^>]*>[^<]*<\/h3>/g,
        `<div class="md:text-right"><h3 class="text-base sm:text-lg font-semibold mb-2 text-center md:text-right">${footerTexts[lang].resources}</h3>`);

      // 替换隐私政策链接
      html = html.replace(/<a[^>]*href="\/[a-z]{2}\/privacy-policy"[^>]*>[^<]*<\/a>/g,
        `<a class="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors" href="/${lang}/privacy-policy">${footerTexts[lang].privacyPolicy}</a>`);

      // 替换服务条款链接
      html = html.replace(/<a[^>]*href="\/[a-z]{2}\/terms-of-service"[^>]*>[^<]*<\/a>/g,
        `<a class="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors" href="/${lang}/terms-of-service">${footerTexts[lang].termsOfService}</a>`);

      // 替换版权信息
      html = html.replace(/<p>©[^<]*<\/p>/g,
        `<p>${footerTexts[lang].copyright}</p>`);

      // 替换中文介绍文本
      if (lang !== 'zh') {
        // 为非中文页面提供对应语言的介绍文本
        const introTexts = {
          en: "Convert text case with one click, free and easy to use",
          es: "Convierte el caso del texto con un clic, gratis y fácil de usar",
          fr: "Convertissez la casse du texte en un clic, gratuit et facile à utiliser",
          de: "Konvertieren Sie Textfälle mit einem Klick, kostenlos und einfach zu bedienen"
        };

        // 替换中文介绍文本
        html = html.replace(/<p class="text-sm sm:text-base">一键实现文本大小写多样转换，免费易用<\/p>/g,
          `<p class="text-sm sm:text-base">${introTexts[lang]}</p>`);
      }

      // 修复"返回首页"链接
      const backToHomeTexts = {
        en: "Back to Home",
        es: "Volver a la Página Principal",
        fr: "Retour à l'Accueil",
        de: "Zurück zur Startseite",
        zh: "返回首页"
      };

      // 替换"返回首页"链接文本
      html = html.replace(/>返回首页</g, `>${backToHomeTexts[lang]}<`);

      // 修复"返回首页"链接的 href 属性
      html = html.replace(/<a[^>]*class="text-primary[^"]*flex items-center"[^>]*href="\/[a-z]{2}\/"[^>]*>/g,
        `<a href="/${lang}/" class="text-primary hover:text-primary-dark flex items-center">`);

      // 更直接的替换方式
      html = html.replace(/<a[^>]*href="\/zh\/"[^>]*class="text-primary[^"]*"[^>]*>/g,
        `<a href="/${lang}/" class="text-primary hover:text-primary-dark flex items-center">`);

      // 以下替换可能不再需要，但为了安全起见，保留它们
      html = html.replace(/<a[^>]*href="\/[a-z]{2}\/privacy-policy"[^>]*class="text-primary[^"]*"[^>]*>/g,
        `<a href="/${lang}/" class="text-primary hover:text-primary-dark flex items-center">`);

      html = html.replace(/<a[^>]*href="\/privacy-policy"[^>]*class="text-primary[^"]*"[^>]*>/g,
        `<a href="/${lang}/" class="text-primary hover:text-primary-dark flex items-center">`);

      html = html.replace(/<a[^>]*href="\/[a-z]{2}\/terms-of-service"[^>]*class="text-primary[^"]*"[^>]*>/g,
        `<a href="/${lang}/" class="text-primary hover:text-primary-dark flex items-center">`);

      html = html.replace(/<a[^>]*href="\/terms-of-service"[^>]*class="text-primary[^"]*"[^>]*>/g,
        `<a href="/${lang}/" class="text-primary hover:text-primary-dark flex items-center">`);

      // 修复链接的 href 属性，确保指向正确的语言版本
      html = html.replace(/<a[^>]*href="\/zh\/"[^>]*>/g, `<a href="/${lang}/">`);
      html = html.replace(/<a[^>]*href="\/en\/"[^>]*>/g, `<a href="/${lang}/">`);
      html = html.replace(/<a[^>]*href="\/es\/"[^>]*>/g, `<a href="/${lang}/">`);
      html = html.replace(/<a[^>]*href="\/fr\/"[^>]*>/g, `<a href="/${lang}/">`);
      html = html.replace(/<a[^>]*href="\/de\/"[^>]*>/g, `<a href="/${lang}/">`);

      // 修复链接的 href 属性，确保指向正确的语言版本的隐私政策和条款页面
      html = html.replace(/<a[^>]*href="\/zh\/privacy-policy"[^>]*>/g, `<a href="/${lang}/privacy-policy">`);
      html = html.replace(/<a[^>]*href="\/en\/privacy-policy"[^>]*>/g, `<a href="/${lang}/privacy-policy">`);
      html = html.replace(/<a[^>]*href="\/es\/privacy-policy"[^>]*>/g, `<a href="/${lang}/privacy-policy">`);
      html = html.replace(/<a[^>]*href="\/fr\/privacy-policy"[^>]*>/g, `<a href="/${lang}/privacy-policy">`);
      html = html.replace(/<a[^>]*href="\/de\/privacy-policy"[^>]*>/g, `<a href="/${lang}/privacy-policy">`);

      html = html.replace(/<a[^>]*href="\/zh\/terms-of-service"[^>]*>/g, `<a href="/${lang}/terms-of-service">`);
      html = html.replace(/<a[^>]*href="\/en\/terms-of-service"[^>]*>/g, `<a href="/${lang}/terms-of-service">`);
      html = html.replace(/<a[^>]*href="\/es\/terms-of-service"[^>]*>/g, `<a href="/${lang}/terms-of-service">`);
      html = html.replace(/<a[^>]*href="\/fr\/terms-of-service"[^>]*>/g, `<a href="/${lang}/terms-of-service">`);
      html = html.replace(/<a[^>]*href="\/de\/terms-of-service"[^>]*>/g, `<a href="/${lang}/terms-of-service">`);

      // 修复隐私政策和条款页面的内容
      if (route.includes('privacy-policy') && lang !== 'zh') {
        // 隐私政策页面标题
        const privacyTitles = {
          en: "Privacy Policy",
          es: "Política de Privacidad",
          fr: "Politique de Confidentialité",
          de: "Datenschutzrichtlinie"
        };

        // 隐私政策页面最后更新日期
        const lastUpdatedTexts = {
          en: "Last Updated: June 1, 2025",
          es: "Última Actualización: 1 de junio de 2025",
          fr: "Dernière Mise à Jour: 1 juin 2025",
          de: "Zuletzt Aktualisiert: 1. Juni 2025"
        };

        // 替换隐私政策页面标题
        html = html.replace(/<h1 class="text-2xl font-bold mb-4">隐私政策<\/h1>/g,
          `<h1 class="text-2xl font-bold mb-4">${privacyTitles[lang]}</h1>`);

        // 替换隐私政策页面最后更新日期
        html = html.replace(/<p class="text-gray-600 mb-6">最后更新：2025年6月1日<\/p>/g,
          `<p class="text-gray-600 mb-6">${lastUpdatedTexts[lang]}</p>`);
      }

      if (route.includes('terms-of-service') && lang !== 'zh') {
        // 条款页面标题
        const termsTitles = {
          en: "Terms of Service",
          es: "Términos de Servicio",
          fr: "Conditions d'Utilisation",
          de: "Nutzungsbedingungen"
        };

        // 条款页面最后更新日期
        const lastUpdatedTexts = {
          en: "Last Updated: June 1, 2025",
          es: "Última Actualización: 1 de junio de 2025",
          fr: "Dernière Mise à Jour: 1 juin 2025",
          de: "Zuletzt Aktualisiert: 1. Juni 2025"
        };

        // 替换条款页面标题
        html = html.replace(/<h1 class="text-2xl font-bold mb-4">服务条款<\/h1>/g,
          `<h1 class="text-2xl font-bold mb-4">${termsTitles[lang]}</h1>`);

        // 替换条款页面最后更新日期
        html = html.replace(/<p class="text-gray-600 mb-6">最后更新：2025年6月1日<\/p>/g,
          `<p class="text-gray-600 mb-6">${lastUpdatedTexts[lang]}</p>`);
      }

      // 修复 JSON-LD 结构化数据中的语言标记
      html = html.replace(/"inLanguage":"zh"/g, `"inLanguage":"${lang}"`);

      // 修复 FAQ 结构化数据
      if (lang !== 'zh') {
        const faqTexts = {
          en: {
            why: {
              question: "Why would I need a text case converter?",
              answer: "Text case converters are useful in many situations: fixing text that was accidentally typed with caps lock on, preparing titles for publications, creating consistent formatting for code or documentation, or simply for stylistic purposes in design and social media."
            },
            secure: {
              question: "Is my text secure when using this tool?",
              answer: "Yes! This tool processes all text directly in your browser. Your content never leaves your device or gets sent to any server, ensuring complete privacy and security."
            },
            mobile: {
              question: "Does this work on mobile devices?",
              answer: "Absolutely! This text case converter is fully responsive and works on smartphones, tablets, and desktop computers."
            },
            languages: {
              question: "What languages are supported?",
              answer: "The converter works with any language that has case distinctions. While it's optimized for English, it handles most Latin-based languages well. For languages without case distinctions (like Chinese or Japanese), only some conversions will apply."
            }
          },
          es: {
            why: {
              question: "¿Por qué necesitaría un convertidor de casos de texto?",
              answer: "Los convertidores de casos de texto son útiles en muchas situaciones: arreglar texto que fue escrito accidentalmente con el bloq mayús activado, preparar títulos para publicaciones, crear formato consistente para código o documentación, o simplemente por propósitos estilísticos en diseño y redes sociales."
            },
            secure: {
              question: "¿Mi texto está seguro al usar esta herramienta?",
              answer: "¡Sí! Esta herramienta procesa todo el texto directamente en tu navegador. Tu contenido nunca sale de tu dispositivo ni se envía a ningún servidor, asegurando completa privacidad y seguridad."
            },
            mobile: {
              question: "¿Funciona en dispositivos móviles?",
              answer: "¡Absolutamente! Este convertidor de casos de texto es completamente responsivo y funciona en smartphones, tablets y computadoras de escritorio."
            },
            languages: {
              question: "¿Qué idiomas son compatibles?",
              answer: "El convertidor funciona con cualquier idioma que tenga distinciones de caso. Aunque está optimizado para español, maneja bien la mayoría de los idiomas basados en latín. Para idiomas sin distinciones de caso (como chino o japonés), solo algunas conversiones se aplicarán."
            }
          },
          fr: {
            why: {
              question: "Pourquoi aurais-je besoin d'un convertisseur de casse de texte?",
              answer: "Les convertisseurs de casse de texte sont utiles dans de nombreuses situations: corriger un texte qui a été accidentellement tapé avec le verrouillage des majuscules activé, préparer des titres pour des publications, créer un formatage cohérent pour du code ou de la documentation, ou simplement pour des raisons stylistiques en design et sur les réseaux sociaux."
            },
            secure: {
              question: "Mon texte est-il sécurisé lors de l'utilisation de cet outil?",
              answer: "Oui! Cet outil traite tout le texte directement dans votre navigateur. Votre contenu ne quitte jamais votre appareil et n'est envoyé à aucun serveur, assurant une confidentialité et une sécurité complètes."
            },
            mobile: {
              question: "Cela fonctionne-t-il sur les appareils mobiles?",
              answer: "Absolument! Ce convertisseur de casse de texte est entièrement responsive et fonctionne sur smartphones, tablettes et ordinateurs de bureau."
            },
            languages: {
              question: "Quelles langues sont prises en charge?",
              answer: "Le convertisseur fonctionne avec n'importe quelle langue qui a des distinctions de casse. Bien qu'il soit optimisé pour le français, il gère bien la plupart des langues basées sur le latin. Pour les langues sans distinctions de casse (comme le chinois ou le japonais), seules certaines conversions s'appliqueront."
            }
          },
          de: {
            why: {
              question: "Warum sollte ich einen Text-Umwandler benötigen?",
              answer: "Text-Umwandler sind in vielen Situationen nützlich: Korrektur von Text, der versehentlich mit aktivierter Feststelltaste getippt wurde, Vorbereitung von Titeln für Publikationen, Erstellung einheitlicher Formatierung für Code oder Dokumentation oder einfach für stilistische Zwecke in Design und sozialen Medien."
            },
            secure: {
              question: "Ist mein Text bei der Verwendung dieses Tools sicher?",
              answer: "Ja! Dieses Tool verarbeitet den gesamten Text direkt in Ihrem Browser. Ihre Inhalte verlassen niemals Ihr Gerät und werden an keinen Server gesendet, was vollständige Privatsphäre und Sicherheit gewährleistet."
            },
            mobile: {
              question: "Funktioniert es auf mobilen Geräten?",
              answer: "Absolut! Dieser Text-Umwandler ist vollständig responsiv und funktioniert auf Smartphones, Tablets und Desktop-Computern."
            },
            languages: {
              question: "Welche Sprachen werden unterstützt?",
              answer: "Der Umwandler funktioniert mit jeder Sprache, die Unterschiede in der Schreibweise hat. Obwohl er für Deutsch optimiert ist, verarbeitet er die meisten lateinbasierten Sprachen gut. Für Sprachen ohne Unterschiede in der Schreibweise (wie Chinesisch oder Japanisch) gelten nur einige Umwandlungen."
            }
          }
        };

        // 替换 FAQ 结构化数据
        const faqData = faqTexts[lang];
        const faqJsonLd = {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": faqData.why.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faqData.why.answer
              }
            },
            {
              "@type": "Question",
              "name": faqData.secure.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faqData.secure.answer
              }
            },
            {
              "@type": "Question",
              "name": faqData.mobile.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faqData.mobile.answer
              }
            },
            {
              "@type": "Question",
              "name": faqData.languages.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faqData.languages.answer
              }
            }
          ]
        };

        // 替换 FAQ 结构化数据
        html = html.replace(/<script type="application\/ld\+json">{"@context":"https:\/\/schema.org","@type":"FAQPage",[^<]*<\/script>/g,
          `<script type="application/ld+json">${JSON.stringify(faqJsonLd)}</script>`);
      }

      console.log(`[SSG] 已完成页面 ${route} 的语言处理，语言: ${lang}`);

      return html;
    }
  }
});
