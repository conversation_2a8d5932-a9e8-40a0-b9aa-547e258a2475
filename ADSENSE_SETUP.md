# Google AdSense 广告位设置指南

## 概述

我已经为您的文本转换工具网站成功添加了 Google AdSense 广告位功能。广告位包括页面顶部横幅和页面底部横幅，支持响应式设计和多语言环境。

## 已实现的功能

### 1. 广告组件
- **AdSense 组件** (`client/src/components/AdSense.tsx`): 核心 AdSense 广告组件
- **AdManager 组件** (`client/src/components/AdManager.tsx`): 广告管理组件，根据位置自动选择广告配置
- **MockAd 组件** (`client/src/components/MockAd.tsx`): 开发环境模拟广告组件

### 2. 广告配置
- **广告配置文件** (`client/src/config/ads.ts`): 集中管理广告位配置
- **环境变量支持**: 通过环境变量控制广告显示

### 3. 广告位置
- **顶部横幅广告**: 位于页面头部下方
- **底部横幅广告**: 位于页面底部，footer 上方

### 4. 响应式设计
- 自动适配桌面端和移动端
- 支持不同尺寸的广告格式

## 配置步骤

### 1. 设置环境变量

创建 `.env.local` 文件（已创建）：

```env
# Google AdSense 配置
VITE_ADSENSE_CLIENT_ID=ca-pub-xxxxxxxxxxxxxxxxx  # 替换为您的实际客户端ID
VITE_ADSENSE_ENABLED=true                        # 是否启用广告
VITE_ADSENSE_SHOW_IN_DEV=false                   # 开发环境是否显示真实广告
```

### 2. 更新广告位ID

编辑 `client/src/config/ads.ts` 文件，将示例广告位ID替换为您的实际广告位ID：

```typescript
export const AD_SLOTS: Record<string, AdConfig> = {
  TOP_BANNER: {
    slot: '您的顶部广告位ID',     // 替换这里
    format: 'horizontal',
    responsive: true,
    description: '页面顶部横幅广告'
  },
  
  BOTTOM_BANNER: {
    slot: '您的底部广告位ID',     // 替换这里
    format: 'horizontal',
    responsive: true,
    description: '页面底部横幅广告'
  }
};
```

### 3. 获取 AdSense 客户端ID和广告位ID

1. 登录 [Google AdSense](https://www.google.com/adsense/)
2. 创建新的广告单元
3. 选择"展示广告"类型
4. 设置广告尺寸为"响应式"
5. 复制生成的客户端ID（格式：ca-pub-xxxxxxxxxxxxxxxxx）
6. 复制广告位ID（数字格式）

## 使用方式

### 在任何页面添加广告

```tsx
import AdManager from '@/components/AdManager';

// 在组件中使用
<AdManager position="top" />     // 顶部广告
<AdManager position="bottom" />  // 底部广告
<AdManager position="sidebar" /> // 侧边栏广告（已配置但未使用）
```

### 直接使用 AdSense 组件

```tsx
import AdSense from '@/components/AdSense';

<AdSense
  slot="您的广告位ID"
  format="horizontal"
  responsive={true}
/>
```

## 开发环境

- 开发环境默认显示模拟广告（灰色虚线框）
- 设置 `VITE_ADSENSE_SHOW_IN_DEV=true` 可在开发环境显示真实广告
- 模拟广告显示广告位信息，便于调试布局

## 生产环境

- 自动加载 Google AdSense 脚本
- 显示真实广告内容
- 支持所有 AdSense 功能（点击跟踪、收入统计等）

## 样式自定义

广告样式在 `client/src/index.css` 中定义：

```css
/* AdSense 广告样式 */
.adsense-container {
  @apply flex justify-center items-center;
}

.top-banner-ad {
  @apply py-2;
}

.bottom-banner-ad {
  @apply py-2;
}
```

## 注意事项

1. **AdSense 政策**: 确保网站内容符合 Google AdSense 政策
2. **广告密度**: 避免在页面上放置过多广告
3. **用户体验**: 确保广告不影响网站的核心功能
4. **加载性能**: 广告脚本延迟加载，不影响页面初始加载速度

## 故障排除

### 广告不显示
1. 检查环境变量配置
2. 确认广告位ID正确
3. 检查 AdSense 账户状态
4. 查看浏览器控制台错误信息

### 开发环境问题
1. 确保 `.env.local` 文件存在
2. 重启开发服务器加载新的环境变量
3. 检查模拟广告是否显示

## 下一步

1. 申请并设置真实的 Google AdSense 账户
2. 获取实际的客户端ID和广告位ID
3. 更新配置文件中的ID
4. 测试广告在生产环境中的显示效果
5. 根据需要调整广告位置和样式
