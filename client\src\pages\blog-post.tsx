import { useTranslation } from 'react-i18next';
import { Link, useParams } from 'react-router-dom';
import SeoHead from '@/components/SeoHead';
import JsonLd from '@/components/JsonLd';
import { useLanguageSync } from '@/hooks/use-language-sync';
import { getBlogPost } from '@/data/blog-posts';
import NotFound from '@/pages/not-found';

const BlogPost = () => {
  const { t, i18n } = useTranslation();
  const { currentLanguage } = useLanguageSync('BlogPost');
  const { slug } = useParams<{ slug: string }>();
  
  if (!slug) {
    return <NotFound />;
  }

  const post = getBlogPost(slug);
  
  if (!post || !post.translations[i18n.language]) {
    return <NotFound />;
  }

  const translation = post.translations[i18n.language];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(i18n.language === 'zh' ? 'zh-CN' : i18n.language, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getBlogUrl = () => {
    return currentLanguage === 'en' ? '/blog' : `/${currentLanguage}/blog`;
  };

  const getHomeUrl = () => {
    return currentLanguage === 'en' ? '/' : `/${currentLanguage}/`;
  };

  const getCurrentPostUrl = () => {
    return currentLanguage === 'en' ? `/blog/${slug}` : `/${currentLanguage}/blog/${slug}`;
  };

  // Convert markdown-like content to HTML (basic implementation)
  const formatContent = (content: string) => {
    return content
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6 mt-8">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4 mt-8">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl md:text-2xl font-bold text-gray-900 mb-3 mt-6">$1</h3>')
      .replace(/^\*\*(.*)\*\*/gim, '<strong class="font-semibold">$1</strong>')
      .replace(/^\* (.*$)/gim, '<li class="mb-2">$1</li>')
      .replace(/^- (.*$)/gim, '<li class="mb-2">$1</li>')
      .replace(/\n\n/g, '</p><p class="text-gray-700 leading-relaxed mb-4">')
      .replace(/^(?!<[h|l|p])(.*$)/gim, '<p class="text-gray-700 leading-relaxed mb-4">$1</p>')
      .replace(/(<li.*<\/li>)/gs, '<ul class="list-disc list-inside mb-6 space-y-2 text-gray-700">$1</ul>')
      .replace(/<\/li><li/g, '</li><li');
  };

  return (
    <>
      <SeoHead 
        title={`${translation.title} - Text Case Converter Blog`}
        description={translation.excerpt}
        canonical={getCurrentPostUrl()}
      />
      
      <JsonLd 
        type="BlogPosting"
        data={{
          "@context": "https://schema.org",
          "@type": "BlogPosting",
          "headline": translation.title,
          "description": translation.excerpt,
          "datePublished": post.publishedAt,
          "dateModified": post.publishedAt,
          "url": `https://textcaseconverter.com${getCurrentPostUrl()}`,
          "inLanguage": currentLanguage,
          "author": {
            "@type": "Organization",
            "name": "Text Case Converter"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Text Case Converter",
            "logo": {
              "@type": "ImageObject",
              "url": "https://textcaseconverter.com/logo.svg"
            }
          },
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `https://textcaseconverter.com${getCurrentPostUrl()}`
          }
        }}
      />

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-600">
            <li>
              <Link 
                to={getHomeUrl()} 
                className="hover:text-primary transition-colors"
              >
                Text Case Converter
              </Link>
            </li>
            <li className="flex items-center">
              <span className="mx-2">/</span>
              <Link 
                to={getBlogUrl()} 
                className="hover:text-primary transition-colors"
              >
                {t('blog.title')}
              </Link>
            </li>
            <li className="flex items-center">
              <span className="mx-2">/</span>
              <span className="text-gray-900 truncate max-w-xs">
                {translation.title}
              </span>
            </li>
          </ol>
        </nav>

        {/* Back to Blog Link */}
        <div className="mb-8">
          <Link 
            to={getBlogUrl()}
            className="inline-flex items-center text-primary hover:text-primary-dark transition-colors"
          >
            <svg 
              className="mr-2 w-4 h-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M15 19l-7-7 7-7" 
              />
            </svg>
            {t('blog.backToBlog')}
          </Link>
        </div>

        {/* Article */}
        <article className="max-w-4xl mx-auto">
          {/* Header */}
          <header className="mb-8">
            <h1 className="text-3xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {translation.title}
            </h1>
            
            <div className="flex items-center text-sm text-gray-500 mb-6">
              <time dateTime={post.publishedAt}>
                {t('blog.publishedOn')} {formatDate(post.publishedAt)}
              </time>
              <span className="mx-2">•</span>
              <span>{post.readingTime} {t('blog.readingTime')}</span>
            </div>
            
            <p className="text-xl text-gray-600 leading-relaxed">
              {translation.excerpt}
            </p>
          </header>

          {/* Content */}
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ 
              __html: formatContent(translation.content) 
            }}
          />
        </article>

        {/* Back to Blog Footer */}
        <div className="max-w-4xl mx-auto mt-12 pt-8 border-t border-gray-200">
          <Link 
            to={getBlogUrl()}
            className="inline-flex items-center text-primary hover:text-primary-dark transition-colors font-semibold"
          >
            <svg 
              className="mr-2 w-4 h-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M15 19l-7-7 7-7" 
              />
            </svg>
            {t('blog.backToBlog')}
          </Link>
        </div>
      </div>
    </>
  );
};

export default BlogPost;
