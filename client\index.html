<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Text Case Converter is a powerful text conversion tool that helps you transform text to different cases such as uppercase, lowercase, title case and more. Supports multiple languages." />
    <meta name="keywords" content="text case converter, uppercase, lowercase, title case, text transformation, sentence case, internationalization" />
    <meta property="og:title" content="Text Case Converter - Modern Text Case Conversion Tool" />
    <meta property="og:description" content="Transform your text to uppercase, lowercase, title case and more with this powerful text conversion tool." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://textcase.top" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Text Case Converter - Modern Text Case Conversion Tool" />
    <meta name="twitter:description" content="Transform your text to uppercase, lowercase, title case and more with this powerful text conversion tool." />
    <meta name="theme-color" content="#3498db">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="icon" href="/favicon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/logo.svg">
    <link rel="manifest" href="/manifest.json">
    <title>Text Case Converter - Modern Text Case Conversion Tool</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-G3CG473PPW"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-G3CG473PPW');
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
