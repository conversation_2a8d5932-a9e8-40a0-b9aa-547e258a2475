import { pgTable, text, serial, integer, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User model for potential future authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  preferredLanguage: text("preferred_language").default("en"),
});

// Text conversion settings for users
export const userSettings = pgTable("user_settings", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  defaultCase: text("default_case").default("sentence"),
  theme: text("theme").default("light"),
  fontSize: text("font_size").default("16px"),
});

// Conversion history for repeat usage
export const conversionHistory = pgTable("conversion_history", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  originalText: text("original_text").notNull(),
  convertedText: text("converted_text").notNull(),
  caseType: text("case_type").notNull(),
  timestamp: text("timestamp").notNull(),
});

// Create schemas for data insertion
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  preferredLanguage: true,
});

export const insertUserSettingsSchema = createInsertSchema(userSettings).pick({
  userId: true,
  defaultCase: true,
  theme: true,
  fontSize: true,
});

export const insertConversionHistorySchema = createInsertSchema(conversionHistory).pick({
  userId: true,
  originalText: true,
  convertedText: true,
  caseType: true,
  timestamp: true,
});

// Define types based on schemas
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertUserSettings = z.infer<typeof insertUserSettingsSchema>;
export type UserSettings = typeof userSettings.$inferSelect;

export type InsertConversionHistory = z.infer<typeof insertConversionHistorySchema>;
export type ConversionHistory = typeof conversionHistory.$inferSelect;
