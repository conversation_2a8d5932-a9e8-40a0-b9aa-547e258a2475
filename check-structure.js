import puppeteer from 'puppeteer';

async function checkPageStructure() {
  console.log('🔍 检查页面结构...');

  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();

    // 设置视口
    await page.setViewport({ width: 1200, height: 800 });

    // 导航到页面
    console.log('🌐 正在访问 http://localhost:5000/');
    await page.goto('http://localhost:5000/', {
      waitUntil: 'networkidle0',
      timeout: 60000
    });

    // 等待一段时间确保页面加载
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 获取页面HTML
    const html = await page.content();
    console.log('📄 页面HTML结构:');
    console.log(html.substring(0, 500) + '...');

    // 检查#root元素
    const rootExists = await page.evaluate(() => {
      return !!document.querySelector('#root');
    });

    console.log(`#root元素${rootExists ? '存在' : '不存在'}`);

    if (rootExists) {
      // 检查#root的子元素
      const rootChildren = await page.evaluate(() => {
        const root = document.querySelector('#root');
        return {
          childCount: root.childNodes.length,
          firstChildTagName: root.firstElementChild ? root.firstElementChild.tagName : 'NONE',
          innerHTML: root.innerHTML.substring(0, 200) + '...'
        };
      });

      console.log('📊 #root元素信息:');
      console.log(`- 子元素数量: ${rootChildren.childCount}`);
      console.log(`- 第一个子元素标签: ${rootChildren.firstChildTagName}`);
      console.log(`- 内部HTML: ${rootChildren.innerHTML}`);
    }

    // 列出所有可能的选择器
    const possibleSelectors = [
      '#root > div',
      '#root > *',
      '.container',
      'header',
      'main',
      'nav'
    ];

    console.log('🔍 检查可能的选择器:');
    for (const selector of possibleSelectors) {
      const exists = await page.evaluate((sel) => {
        return !!document.querySelector(sel);
      }, selector);

      console.log(`- ${selector}: ${exists ? '存在' : '不存在'}`);
    }

  } catch (error) {
    console.error('❌ 检查过程中出错:', error);
  } finally {
    await browser.close();
  }
}

checkPageStructure();
