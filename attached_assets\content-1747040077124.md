- [![Convert Case](https://convertcase.net/img/logo.svg?1747032920628)![Convert Case](https://convertcase.net/img/logo-dark.svg?1747032920628)](https://convertcase.net/)
- Text Modification/Formatting
  - [APA Format Converter & Generator](https://convertcase.net/apa-format-converter-generator/)
  - [Big Text Converter](https://convertcase.net/big-text-generator/)
  - [Bold Text Generator](https://convertcase.net/bold-text-converter/)
  - [Bubble Text Generator](https://convertcase.net/bubble-text-generator/)
  - [Cursed Text Tool](https://convertcase.net/cursed-text/)
  - [Discord Font Generator](https://convertcase.net/discord-fonts-generator/)
  - [Duplicate Line Remover](https://convertcase.net/duplicate-line-remover/)
  - [Facebook Font Generator](https://convertcase.net/facebook-font-generator/)
  - [Fonts for Instagram](https://convertcase.net/instagram-fonts/)
  - [Invisible Text Generator](https://convertcase.net/invisible-text-generator/)
  - [Italic Text Converter](https://convertcase.net/italic-text-converter/)
  - [Mirror Text Generator](https://convertcase.net/mirror-text-generator/)
  - [Phonetic Spelling Generator](https://convertcase.net/phonetic-spelling-generator/)
  - [Pig Latin Translator](https://convertcase.net/pig-latin-translator/)
  - [Plain Text Converter](https://convertcase.net/plain-text-converter/)
  - [Remove Letters & Characters](https://convertcase.net/letter-character-removal-tool/)
  - [Remove Line Breaks](https://convertcase.net/remove-line-breaks/)
  - [Remove Text Formatting](https://convertcase.net/remove-text-formatting/)
  - [Reverse Text Generator](https://convertcase.net/reverse-text-generator/)
  - [Sentence Case Converter](https://convertcase.net/sentence-case/)
  - [Slash Text Generator Tool](https://convertcase.net/slash-text-generator/)
  - [Small Text Generator](https://convertcase.net/small-text-generator/)
  - [Stacked Text Generator](https://convertcase.net/stacked-text-generator/)
  - [Strikethrough Text Generator](https://convertcase.net/strikethrough-text-generator/)
  - [Subscript Generator](https://convertcase.net/subscript-generator/)
  - [Superscript Generator](https://convertcase.net/superscript-generator/)
  - [Title Case Converter](https://convertcase.net/title-case-converter/)
  - [Underline Text Generator](https://convertcase.net/underline-text/)
  - [Unicode Text Converter](https://convertcase.net/unicode-text-converter/)
  - [Upside Down Text Generator](https://convertcase.net/upside-down-text-generator/)
  - [Whitespace Remover](https://convertcase.net/whitespace-remover/)
  - [Wide Text Generator](https://convertcase.net/vaporwave-wide-text-generator/)
  - [Wingdings Converter](https://convertcase.net/wingdings-converter/)
  - [X & Twitter Font Generator](https://convertcase.net/twitter-font-generator/)
  - [Zalgo Glitch Text Generator](https://convertcase.net/glitch-text-converter/)
- Code & Data Translation
  - [Base64 Encoder/Decoder](https://convertcase.net/base64-decode-encode/)
  - [Binary Code Translator](https://convertcase.net/binary-code-translator/)
  - [CSS Formatter](https://convertcase.net/css-formatter/)
  - [CSV to JSON Converter](https://convertcase.net/csv-to-json/)
  - [Caesar Cipher Tool](https://convertcase.net/caesar-cipher-encryption/)
  - [HTML Formatter, Minifier & Beautifier](https://convertcase.net/html-formatter/)
  - [Hex to Text Converter](https://convertcase.net/hex-to-text-converter/)
  - [JSON Formatter & Minifier](https://convertcase.net/json-formatter/)
  - [JSON Stringify Text Generator](https://convertcase.net/json-stringify-text/)
  - [JavaScript Formatter & Beautifier](https://convertcase.net/javascript-formatter/)
  - [MD5 Hash Generator](https://convertcase.net/md5-hash-generator/)
  - [Morse Code Translator](https://convertcase.net/morse-code-translator/)
  - [Number Sorter](https://convertcase.net/number-sorter/)
  - [ROT13 Encoder/Decoder](https://convertcase.net/rot13-encoder-decoder/)
  - [Regex Tester Tool](https://convertcase.net/regex-tester-tool/)
  - [Slugify URL Generator](https://convertcase.net/slugify-url-handle-generator/)
  - [URL Encode and Decode](https://convertcase.net/url-encode-decode/)
  - [UTF-8 Encoder/Decoder](https://convertcase.net/utf8-encoding/)
  - [UTM Builder Generator](https://convertcase.net/utm-generator/)
  - [XML Formatter](https://convertcase.net/xml-formatter/)
- Image Tools
  - [AI Image to Text Converter](https://convertcase.net/image-to-text-converter/)
  - [Ascii Art Generator](https://convertcase.net/ascii-art-generator/)
  - [JPG to PNG Converter](https://convertcase.net/jpg-to-png/)
  - [JPG to WebP Converter](https://convertcase.net/jpg-to-webp/)
  - [PNG to JPG Converter](https://convertcase.net/png-to-jpg/)
  - [PNG to WebP Converter](https://convertcase.net/png-to-webp/)
  - [SVG to PNG Converter](https://convertcase.net/svg-to-png/)
  - [WebP to JPG Converter](https://convertcase.net/webp-to-jpg/)
  - [WebP to PNG Converter](https://convertcase.net/webp-to-png/)
- Random Generators
  - [Random Choice Generator](https://convertcase.net/random-choice-generator/)
  - [Random Date Generator](https://convertcase.net/random-date-generator/)
  - [Random IP Address Generator](https://convertcase.net/random-ip-address-generator/)
  - [Random Letter Generator](https://convertcase.net/random-letter-generator/)
  - [Random Month Generator](https://convertcase.net/random-month-generator/)
  - [Random Number Generator](https://convertcase.net/random-number-generator/)
  - [Strong Password Generator](https://convertcase.net/strong-password-generator/)
  - [UUID Generator](https://convertcase.net/uuid-generator/)
- Misc. Tools
  - [NATO Phonetic Alphabet Translator](https://convertcase.net/nato-alphabet-translator/)
  - [Online Notepad](https://onlinenotepad.net/)
  - [Online Sentence Counter](https://convertcase.net/online-sentence-counter/)
  - [Repeat Text Generator](https://convertcase.net/repeat-text/)
  - [Roman Numeral Date Converter](https://convertcase.net/roman-numeral-date-converter/)
  - [Sort Words Alphabetically](https://convertcase.net/sort-alphabetically/)
  - [Text Replacement Tool](https://convertcase.net/replace-text/)
  - [Word Frequency Counter](https://convertcase.net/word-frequency-counter/)
- Resources
  - [About](https://convertcase.net/about/)
  - [Chrome Browser Extension](https://convertcase.net/browser-extension/)
  - [Contact](https://convertcase.net/contact/)
  - [Convert Case Mobile App](https://convertcase.net/mobile-app/)
  - [Online Text Tools](https://convertcase.net/online-text-tools/)
  - [Suggest a Tool](https://convertcase.net/suggest-tool/)
- Languages
  - [Deutsch](https://convertcase.net/de/)
  - [Ελληνικά](https://convertcase.net/el/)
  - [English](https://convertcase.net/)
  - [Español](https://convertcase.net/es/)
  - [Filipino](https://convertcase.net/fil/)
  - [Français](https://convertcase.net/fr/)
  - [अंग्रेज़ी](https://convertcase.net/hi/)
  - [Magyar](https://convertcase.net/hu/)
  - [Italiano](https://convertcase.net/it/)
  - [Polski](https://convertcase.net/pl/)
  - [Português](https://convertcase.net/pt/)
  - [Türkçe](https://convertcase.net/tr/)
  - [Українська](https://convertcase.net/ua/)
  - [No results - Suggest a tool?](https://convertcase.net/suggest-tool/)

# Accidentally left the caps lock on and typed something, but can't be bothered to start again and retype it all?

Simply enter your text and choose the case you want to convert it to.

Sentence caselower caseUPPER CASECapitalized CaseaLtErNaTiNg cAsETitle CaseInVeRsE CaSeDownload TextCopy to ClipboardClear [![Ko-fi Logo](https://convertcase.net/img/kofi.webp) Buy me a Coffee](https://ko-fi.com/convertcase)

Character Count: 0 \| Word Count: 0 \| Sentence Count: 0 \| Line Count: 0

## Welcome to the Convert Case Text Generator Tool

A very handy online text tool where you can change between lower case and upper case letters, where you can capitalize, uncapitalize, convert to mix case and transform your text. Explore the options below:

### [Sentence Case](https://convertcase.net/sentence-case/)

The sentence case converter will allow you to paste any text you’d like, and it will automatically transform it to a fully formed structured sentence.

It works by capitalizing the very first letter in each sentence, and will then go on to transform the rest of the text into lowercase as well as converting i’s into I’s. Every letter after a full stop will get converted into an upper case letter.

Note: it won’t, however, capitalize names or places.

This is an example of sentence case.

### Lower Case

If you are wondering how to uncapitalize text, this is exactly what the lower case text converter will allow you to do - it transforms all the letters in your text into lowercase letters. Simply copy the text that you need generating into lower case and paste the text into the box above and select the ‘lower case’ tab.

this is an example of lower case.

### Upper Case

The upper case transformer will take any text that you have and will generate all the letters into upper case ones. It will essentially make all lower case letters into CAPITALS (as well as keep upper case letters as upper case letters).

To do this, you simply have to select the text that you need changing and pasting into the box above and then select the UPPER CASE tab.

THIS IS AN EXAMPLE OF UPPER CASE.

### Capitalized Case

The capitalized case converter will automatically convert the starting letter of every word into an upper case and will leave the remaining letters as lower case ones.

Simply copy the content that you will like to generate into this format, then paste into the box form above and select the Capitalized Case tab.

This Is An Example Of Capitalized Case.

### Alternating Case

The alternating case converter will allow you to transform your text (no matter the current format) into text that alternates between lower case and upper case. It will generate a capital letter and then a lower case letter within the same word.

tHiS Is aN ExAmPlE Of aLtErNaTiNg cAsE.

### [Title Case](https://convertcase.net/title-case-converter/)

The title case converter is perfect for those who are a bit unsure on how to title an upcoming essay. It essentially ensures the correct letters are capitalized within the context of a title. Words such as “an” will be left all in lower case and words that are important will be converted such as “Title”.

This Is an Example of Title Case.

### [Small Text Generator](https://convertcase.net/small-text-generator/)

The small text generator simply takes regular sized text and converts it into small text (specifically “small caps” as well as “Superscript” text. Write out your normal text and see it get converted into the small text font.

Tʜɪꜱ ɪꜱ ᴀɴ ᴇxᴀᴍᴘʟᴇ ᴏꜰ ᴛɪɴʏ ᴛᴇxᴛ ᴄᴀꜱᴇ ꜱᴍᴀʟʟ ᴄᴀᴘꜱ

ᵗʰⁱˢ ⁱˢ ᵃⁿ ᵉˣᵃᵐᵖˡᵉ ᵒᶠ ᵗⁱⁿʸ ᵗᵉˣᵗ ᶜᵃˢᵉ ˢᵘᵖᵉʳˢᶜʳⁱᵖᵗ

### [Wide Text Generator](https://convertcase.net/vaporwave-wide-text-generator/)

If you are looking to widen the look of your text, the widening text generator is great for this, otherwise known as the Aesthetic Font and text generator. Simply type your normal text and see it get wider and wider.

Ｔｈｉｓ ｉｓ ａｎ ｅｘａｍｐｌｅ ｏｆ ｗｉｄｅ ａｅｓｔｈｅｔｉｃ ｔｅｘｔ。

### [Strikethrough Text Generator](https://convertcase.net/strikethrough-text-generator/)

If you want a quick way of crossing out your text, this is a great tool. Type out the normal text you want to be lined through and you will see it get automatically generated, ready for you to copy and paste.

T̶h̶i̶s̶ ̶i̶s̶ ̶a̶n̶ ̶e̶x̶a̶m̶p̶l̶e̶ ̶o̶f̶ ̶s̶t̶r̶i̶k̶e̶t̶h̶r̶o̶u̶g̶h̶ ̶t̶e̶x̶t̶.̶

### [Reverse Text Generator](https://convertcase.net/reverse-text-generator/)

If you want a fast and quick way of making your text go back to front, the reverse text generator is great. Write out your text like normal and then see it get flipped.

.txet sdrawkcab fo ecnetnes elpmaxe na si sihT

### [Upside down Text Generator](https://convertcase.net/upside-down-text-generator/)

Similar to the mirror text generator as wella s the reverse text generator, you can flip your text upside down as well as back to front with the upside down text generator.

˙ʇxǝʇ uʍop ǝpᴉsdn ɟo ǝldɯɐxǝ uɐ sᴉ sᴉɥ┴

### [Morse Code Translator](https://convertcase.net/morse-code-translator/)

Whether you are looking to translate morse code messages into simple english or the other way around, this online translation generator can do just that. It also comes with an audio player to hear it.

### [Binary Code Translator](https://convertcase.net/binary-code-translator/)

Translate binary code into English and English into binary code with the following generator. Type out regularly and get a series of 0’s and 1’s in return.

01000010 01101001 01101110 01100001 01110010 01111001 00100000 01000011 01101111 01100100 01100101 00100000 01010100 01110010 01100001 01101110 01110011 01101100 01100001 01110100 01101111 01110010

### [Bold Text Generator](https://convertcase.net/bold-text-converter/)

A quick way to bold your text online. Simply type out the normal text into the tool and see it automatically get converted to the bold versio which you can copy and paste across.

𝗧𝗵𝗶𝘀 𝗶𝘀 𝗮𝗻 𝗲𝘅𝗮𝗺𝗽𝗹𝗲 𝗼𝗳 𝗯𝗼𝗹𝗱 𝘁𝗲𝘅𝘁.

### [Italic Text Converter](https://convertcase.net/italic-text-converter/)

If you want to italicise your font, this is a great tool. Type out your normal text and see it automatically write on a slant, which you can copy and paste where you want.

𝘛𝘩𝘪𝘴 𝘪𝘴 𝘢𝘯 𝘦𝘹𝘢𝘮𝘱𝘭𝘦 𝘰𝘧 𝘪𝘵𝘢𝘭𝘪𝘤 𝘵𝘦𝘹𝘵.

### [Underline Text Generator](https://convertcase.net/underline-text/)

Underline your text online with this underliner tool. Write out or paste across the content you want to be underlined and then you will see it automatically generated underlined - which you can copy and paste across to where you want.

T̲h̲i̲s̲ ̲i̲s̲ ̲a̲n̲ ̲e̲x̲a̲m̲p̲l̲e̲ ̲o̲f̲ ̲u̲n̲d̲e̲r̲l̲i̲n̲e̲ ̲t̲e̲x̲t̲.̲

### [Mirror Text Generator](https://convertcase.net/mirror-text-generator/)

The mirror text is basically the exact same text that you would get when you have your text facing a mirror. Print out the text and hold it up to a mirror and it should read the correct way.

.ƚxɘƚ ɿoɿɿim ʇo ɘlqmɒxɘ nɒ ƨi ƨiʜT

### [Unicode Text Converter](https://convertcase.net/unicode-text-converter/)

Explore our unicode text generator for a wide range of different fonts. Type in your normal text and then choose whatever you want to use.

### [Zalgo Glitch Text Generator](https://convertcase.net/glitch-text-converter/)

Want to produce funky and glitchy text? \`This Zalgo text will do just that. Type out what you would normally want to and then see it get turned into the Zalgo font.

T̵̡̧̨̖̪̗̠͔̰̓̈́̕͜ẖ̷̙̪̮̭̖̮̯̘͋͐̃̅͌̈́̆̕ͅȉ̶̛̫̟͎͋͗̓s̵̡͔̻͆̄͘ i̴̟͔͚͔͗͠ś̴̳̗͙ a̵̧̳͇̜̪̣̅̓n̸̛̰̼͇̱͐̎̀̽̌̐̿̈́̓ͅ ḛ̴̟̉͗x̷̬̩̀̕à̵̡̭̫̤̯͇̫͓̘͊̑͐̎̍͠m̶͎̦̾̾͠p̷̗̬̯̰͙̣̮̋̈́͆͊̇̿̋l̸̨̘̞̗̩̞̱͖̏̍̽̆͜͜e̸̱̹̬̞̹̍̇̓ͅ o̷̊ͅḟ̴͈̆̔̾̒́ g̴̤̪̻̰̗̀̓̎͋͝l̷̨͇͔͈̤̝̊̀̂͛͜į̴̡̦̼̄̏̏͛̒ẗ̶̙̻̜̺͉́̽̒̅̀̐͝c̸̛̛͇̱͇̮͔͔͙̼̀̈́̀͜h̵̙̘̣̻͈̫͇̜͂́̍͊͗̀͜ t̷̬̹̜̼̫̘͒̊ę̷̰̬̠̭̠͉̳̐́̒͑͋̓̎̽̈́͠x̷̢̢̛͓̜͚̖̜͕̼̙̌̍ţ̵̖͖̜̜̪́̎͒̚͜͝.̷̛̬̖̳̻̪̩̾̾̆̄̓͋͒̚͠

### Social Media Font Generators

We have a range of font generators you can use across social media platforms including: [Facebook](https://convertcase.net/facebook-font-generator/), [Instagram](https://convertcase.net/instagram-fonts/), [Discord](https://convertcase.net/discord-fonts-generator/) and [Twitter/X](https://convertcase.net/twitter-font-generator/).

### Image Conversion

IF you are looking to quickly convert images between formats, you can do so quickly and efficiently with our image conversion tools: [JPG to WebP](https://convertcase.net/jpg-to-webp/), [WebP to JPG](https://convertcase.net/webp-to-jpg/), [ASCII Art Generator](https://convertcase.net/ascii-art-generator/) and our [AI Image to Text Converter](https://convertcase.net/image-to-text-converter/).

Copyright ©2006-2025 Convert Case Ltd \| Last Updated (May 2025) \| Concept by [Jason Gillyon](https://jasongillyon.co.uk/) \| [Privacy Policy](https://convertcase.net/privacy/) \| [Terms of Service](https://convertcase.net/terms/) \| [Site Map](https://convertcase.net/sitemap/) \| Theme: [AutoDarkLight](https://convertcase.net/#)

Registered in England and Wales. Company number: 11614495.

Registered office address: C4DI @TheDock, 31-38 Queen Street, Hull, England, HU1 1UU

 [Slava Ukraini, Glory to Ukraine!](https://savelife.in.ua/en/donate-en/ "Slava Ukraini, Glory to Ukraine!")[iframe](about:blank)