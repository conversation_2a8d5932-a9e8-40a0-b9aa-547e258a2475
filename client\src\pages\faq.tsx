import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useState } from 'react';
import SeoHead from '@/components/SeoHead';
import JsonLd from '@/components/JsonLd';
import { useLanguageSync } from '@/hooks/use-language-sync';

interface FaqItem {
  id: string;
  question: string;
  answer: string;
}

const FAQ = () => {
  const { t, i18n } = useTranslation();
  
  // 使用自定义hook确保语言同步
  const { currentLanguage } = useLanguageSync('FAQ');
  
  // 跟踪当前打开的FAQ项目
  const [openItem, setOpenItem] = useState<string | null>(null);

  const toggleItem = (id: string) => {
    setOpenItem(openItem === id ? null : id);
  };

  // 获取FAQ数据
  const faqs: FaqItem[] = [
    {
      id: 'whatIs',
      question: t('faqPage.questions.whatIs.question'),
      answer: t('faqPage.questions.whatIs.answer')
    },
    {
      id: 'howToUse',
      question: t('faqPage.questions.howToUse.question'),
      answer: t('faqPage.questions.howToUse.answer')
    },
    {
      id: 'supportedLanguages',
      question: t('faqPage.questions.supportedLanguages.question'),
      answer: t('faqPage.questions.supportedLanguages.answer')
    },
    {
      id: 'dataPrivacy',
      question: t('faqPage.questions.dataPrivacy.question'),
      answer: t('faqPage.questions.dataPrivacy.answer')
    },
    {
      id: 'mobileSupport',
      question: t('faqPage.questions.mobileSupport.question'),
      answer: t('faqPage.questions.mobileSupport.answer')
    },
    {
      id: 'cost',
      question: t('faqPage.questions.cost.question'),
      answer: t('faqPage.questions.cost.answer')
    },
    {
      id: 'textLimits',
      question: t('faqPage.questions.textLimits.question'),
      answer: t('faqPage.questions.textLimits.answer')
    },
    {
      id: 'browserSupport',
      question: t('faqPage.questions.browserSupport.question'),
      answer: t('faqPage.questions.browserSupport.answer')
    }
  ];

  // 获取FAQ数据用于JSON-LD
  const getFaqData = () => {
    return faqs.map(faq => ({
      question: faq.question,
      answer: faq.answer
    }));
  };

  return (
    <>
      {/* SEO头部标签，包含canonical和hreflang */}
      <SeoHead path="/faq" />

      {/* 添加结构化数据 */}
      <JsonLd type="breadcrumb" path="/faq" />
      <JsonLd type="faq" faqData={getFaqData()} />

      <div className="container mx-auto px-4 py-8">
        {/* 面包屑导航 */}
        <div className="mb-6">
          <Link 
            to={i18n.language === 'en' ? '/' : `/${i18n.language}/`} 
            className="text-primary hover:text-primary-dark flex items-center"
          >
            <span className="material-icons text-sm mr-1">arrow_back</span>
            {t('faqPage.backToHome')}
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h1 className="text-2xl font-bold mb-4">{t('faqPage.title')}</h1>
          <p className="text-gray-600 mb-6">{t('faqPage.subtitle')}</p>

          <div className="space-y-4">
            {faqs.map((faq) => (
              <div key={faq.id} className="border border-gray-200 rounded-lg">
                <button 
                  onClick={() => toggleItem(faq.id)}
                  className="flex justify-between w-full px-4 py-3 text-left text-gray-900 bg-gray-50 hover:bg-gray-100 rounded-lg focus:outline-none focus-visible:ring focus-visible:ring-primary"
                >
                  <span className="font-medium">{faq.question}</span>
                  <span className="material-icons">
                    {openItem === faq.id ? 'remove' : 'add'}
                  </span>
                </button>
                {openItem === faq.id && (
                  <div className="px-4 py-3 text-gray-700 bg-white">
                    {faq.answer}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default FAQ;
