import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import SeoHead from '@/components/SeoHead';
import JsonLd from '@/components/JsonLd';
import { useLanguageSync } from '@/hooks/use-language-sync';
import { getBlogPostsByLanguage } from '@/data/blog-posts';

const Blog = () => {
  const { t, i18n } = useTranslation();
  const { currentLanguage } = useLanguageSync('Blog');

  const blogPosts = getBlogPostsByLanguage(i18n.language);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(i18n.language === 'zh' ? 'zh-CN' : i18n.language, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getBlogUrl = (slug: string) => {
    return currentLanguage === 'en' ? `/blog/${slug}` : `/${currentLanguage}/blog/${slug}`;
  };

  const getHomeUrl = () => {
    return currentLanguage === 'en' ? '/' : `/${currentLanguage}/`;
  };

  return (
    <>
      <SeoHead
        title={`${t('blog.title')} - Text Case Converter`}
        description={t('blog.subtitle')}
        canonical={currentLanguage === 'en' ? '/blog' : `/${currentLanguage}/blog`}
      />

      <JsonLd
        type="Blog"
        data={{
          "@context": "https://schema.org",
          "@type": "Blog",
          "name": `${t('blog.title')} - Text Case Converter`,
          "description": t('blog.subtitle'),
          "url": `https://textcaseconverter.com${currentLanguage === 'en' ? '/blog' : `/${currentLanguage}/blog`}`,
          "inLanguage": currentLanguage,
          "blogPost": blogPosts.map(post => ({
            "@type": "BlogPosting",
            "headline": post.translations[i18n.language]?.title,
            "description": post.translations[i18n.language]?.excerpt,
            "datePublished": post.publishedAt,
            "url": `https://textcaseconverter.com${getBlogUrl(post.slug)}`,
            "author": {
              "@type": "Organization",
              "name": "Text Case Converter"
            }
          }))
        }}
      />

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-600">
            <li>
              <Link
                to={getHomeUrl()}
                className="hover:text-primary transition-colors"
              >
                Text Case Converter
              </Link>
            </li>
            <li className="flex items-center">
              <span className="mx-2">/</span>
              <span className="text-gray-900">{t('blog.title')}</span>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            {t('blog.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('blog.subtitle')}
          </p>
        </div>

        {/* Blog Posts Grid */}
        <div className="max-w-4xl mx-auto">
          {blogPosts.length > 0 ? (
            <div className="grid gap-8 md:gap-12">
              {blogPosts.map((post) => {
                const translation = post.translations[i18n.language];
                if (!translation) return null;

                return (
                  <article
                    key={post.id}
                    className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                  >
                    <div className="p-6 md:p-8">
                      <div className="flex items-center text-sm text-gray-500 mb-4">
                        <time dateTime={post.publishedAt}>
                          {t('blog.publishedOn')} {formatDate(post.publishedAt)}
                        </time>
                        <span className="mx-2">•</span>
                        <span>{post.readingTime} {t('blog.readingTime')}</span>
                      </div>

                      <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight">
                        <Link
                          to={getBlogUrl(post.slug)}
                          className="hover:text-primary transition-colors"
                        >
                          {translation.title}
                        </Link>
                      </h2>

                      <p className="text-gray-600 text-lg leading-relaxed mb-6">
                        {translation.excerpt}
                      </p>

                      <Link
                        to={getBlogUrl(post.slug)}
                        className="inline-flex items-center text-primary font-semibold hover:text-primary-dark transition-colors"
                      >
                        {t('blog.readMore')}
                        <svg
                          className="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </Link>
                    </div>
                  </article>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {t('blog.noPostsFound')}
              </h3>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Blog;
