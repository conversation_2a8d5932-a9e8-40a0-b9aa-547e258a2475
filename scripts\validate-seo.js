import fs from 'fs';
import path from 'path';

// SEO验证配置
const config = {
  siteUrl: 'https://textcase.top',
  clientPublicDir: './client/public',
  distPublicDir: './dist/public',
  expectedLanguages: ['en', 'es', 'fr', 'de', 'zh'],
  expectedPages: ['/', '/blog', '/privacy-policy', '/terms-of-service'],
  expectedBlogPosts: ['text-conversion-tips']
};

// 验证XML Sitemap
function validateSitemap() {
  console.log('🔍 验证 XML Sitemap...\n');
  
  const sitemapPaths = [
    path.join(config.clientPublicDir, 'sitemap.xml'),
    path.join(config.distPublicDir, 'sitemap.xml')
  ];
  
  let allValid = true;
  
  sitemapPaths.forEach(sitemapPath => {
    console.log(`📄 检查: ${sitemapPath}`);
    
    if (!fs.existsSync(sitemapPath)) {
      console.log(`❌ 文件不存在: ${sitemapPath}`);
      allValid = false;
      return;
    }
    
    const content = fs.readFileSync(sitemapPath, 'utf-8');
    
    // 基本XML格式检查
    if (!content.includes('<?xml version="1.0" encoding="UTF-8"?>')) {
      console.log('❌ 缺少XML声明');
      allValid = false;
    }
    
    if (!content.includes('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
      console.log('❌ 缺少正确的urlset声明');
      allValid = false;
    }
    
    // 检查必需的页面
    const missingPages = [];
    config.expectedPages.forEach(page => {
      config.expectedLanguages.forEach(lang => {
        const langPath = lang === 'en' ? '' : `/${lang}`;
        const pagePath = page === '/' ? '' : page;
        const expectedUrl = `${config.siteUrl}${langPath}${pagePath}`;
        
        if (!content.includes(`<loc>${expectedUrl}</loc>`)) {
          missingPages.push(expectedUrl);
        }
      });
    });
    
    // 检查博客文章
    config.expectedBlogPosts.forEach(slug => {
      config.expectedLanguages.forEach(lang => {
        const langPath = lang === 'en' ? '' : `/${lang}`;
        const expectedUrl = `${config.siteUrl}${langPath}/blog/${slug}`;
        
        if (!content.includes(`<loc>${expectedUrl}</loc>`)) {
          missingPages.push(expectedUrl);
        }
      });
    });
    
    if (missingPages.length > 0) {
      console.log('❌ 缺少以下URL:');
      missingPages.slice(0, 5).forEach(url => console.log(`   - ${url}`));
      if (missingPages.length > 5) {
        console.log(`   ... 还有 ${missingPages.length - 5} 个URL`);
      }
      allValid = false;
    }
    
    // 检查多语言链接
    const hreflangCount = (content.match(/hreflang="/g) || []).length;
    const expectedHreflangCount = (config.expectedPages.length + config.expectedBlogPosts.length) * 
                                  config.expectedLanguages.length * 
                                  (config.expectedLanguages.length + 1); // +1 for x-default
    
    if (hreflangCount < expectedHreflangCount * 0.8) { // 允许80%的容错率
      console.log(`⚠️ hreflang链接数量可能不足: ${hreflangCount} (期望约 ${expectedHreflangCount})`);
    }
    
    // 检查lastmod日期格式
    const lastmodMatches = content.match(/<lastmod>([^<]+)<\/lastmod>/g);
    if (lastmodMatches) {
      const invalidDates = lastmodMatches.filter(match => {
        const date = match.match(/<lastmod>([^<]+)<\/lastmod>/)[1];
        return !/^\d{4}-\d{2}-\d{2}$/.test(date);
      });
      
      if (invalidDates.length > 0) {
        console.log('❌ 发现无效的lastmod日期格式:');
        invalidDates.slice(0, 3).forEach(date => console.log(`   - ${date}`));
        allValid = false;
      }
    }
    
    // 统计信息
    const urlCount = (content.match(/<url>/g) || []).length;
    console.log(`📊 包含 ${urlCount} 个URL`);
    
    if (allValid) {
      console.log('✅ Sitemap验证通过\n');
    } else {
      console.log('❌ Sitemap验证失败\n');
    }
  });
  
  return allValid;
}

// 验证Robots.txt
function validateRobotsTxt() {
  console.log('🔍 验证 Robots.txt...\n');
  
  const robotsPaths = [
    path.join(config.clientPublicDir, 'robots.txt'),
    path.join(config.distPublicDir, 'robots.txt')
  ];
  
  let allValid = true;
  
  robotsPaths.forEach(robotsPath => {
    console.log(`📄 检查: ${robotsPath}`);
    
    if (!fs.existsSync(robotsPath)) {
      console.log(`❌ 文件不存在: ${robotsPath}`);
      allValid = false;
      return;
    }
    
    const content = fs.readFileSync(robotsPath, 'utf-8');
    
    // 检查基本结构
    if (!content.includes('User-agent: *')) {
      console.log('❌ 缺少通用User-agent声明');
      allValid = false;
    }
    
    if (!content.includes('Allow: /')) {
      console.log('❌ 缺少Allow指令');
      allValid = false;
    }
    
    if (!content.includes(`Sitemap: ${config.siteUrl}/sitemap.xml`)) {
      console.log('❌ 缺少Sitemap链接或链接不正确');
      allValid = false;
    }
    
    // 检查是否有禁止访问的路径
    const disallowCount = (content.match(/Disallow:/g) || []).length;
    if (disallowCount === 0) {
      console.log('⚠️ 没有设置任何Disallow规则');
    }
    
    // 检查爬虫延迟设置
    if (!content.includes('Crawl-delay:')) {
      console.log('⚠️ 没有设置爬虫延迟');
    }
    
    // 检查特定搜索引擎配置
    const searchEngines = ['Googlebot', 'Bingbot'];
    searchEngines.forEach(bot => {
      if (!content.includes(`User-agent: ${bot}`)) {
        console.log(`⚠️ 没有为 ${bot} 设置特定规则`);
      }
    });
    
    // 检查是否阻止了有害爬虫
    const blockedBots = ['AhrefsBot', 'MJ12bot', 'DotBot'];
    const blockedCount = blockedBots.filter(bot => 
      content.includes(`User-agent: ${bot}`) && 
      content.includes('Disallow: /')
    ).length;
    
    console.log(`📊 阻止了 ${blockedCount}/${blockedBots.length} 个有害爬虫`);
    
    if (allValid) {
      console.log('✅ Robots.txt验证通过\n');
    } else {
      console.log('❌ Robots.txt验证失败\n');
    }
  });
  
  return allValid;
}

// 生成SEO报告
function generateSEOReport() {
  console.log('📋 生成SEO报告...\n');
  
  const report = {
    timestamp: new Date().toISOString(),
    sitemap: {
      valid: false,
      urlCount: 0,
      languages: config.expectedLanguages.length,
      pages: config.expectedPages.length,
      blogPosts: config.expectedBlogPosts.length
    },
    robots: {
      valid: false,
      hasBasicRules: false,
      hasCrawlDelay: false,
      blockedBots: 0
    },
    recommendations: []
  };
  
  // 检查sitemap
  const sitemapPath = path.join(config.clientPublicDir, 'sitemap.xml');
  if (fs.existsSync(sitemapPath)) {
    const content = fs.readFileSync(sitemapPath, 'utf-8');
    report.sitemap.urlCount = (content.match(/<url>/g) || []).length;
    report.sitemap.valid = content.includes('<?xml version="1.0"') && 
                          content.includes('<urlset xmlns=');
  }
  
  // 检查robots.txt
  const robotsPath = path.join(config.clientPublicDir, 'robots.txt');
  if (fs.existsSync(robotsPath)) {
    const content = fs.readFileSync(robotsPath, 'utf-8');
    report.robots.valid = content.includes('User-agent: *') && 
                         content.includes('Sitemap:');
    report.robots.hasBasicRules = content.includes('Allow:') && 
                                 content.includes('Disallow:');
    report.robots.hasCrawlDelay = content.includes('Crawl-delay:');
    report.robots.blockedBots = (content.match(/User-agent: \w+Bot[\s\S]*?Disallow: \//g) || []).length;
  }
  
  // 生成建议
  if (!report.sitemap.valid) {
    report.recommendations.push('修复XML Sitemap格式问题');
  }
  
  if (!report.robots.valid) {
    report.recommendations.push('修复Robots.txt基本配置');
  }
  
  if (!report.robots.hasCrawlDelay) {
    report.recommendations.push('考虑添加爬虫延迟设置');
  }
  
  if (report.robots.blockedBots < 3) {
    report.recommendations.push('考虑阻止更多有害爬虫');
  }
  
  console.log('📊 SEO报告:');
  console.log(`   Sitemap: ${report.sitemap.valid ? '✅' : '❌'} (${report.sitemap.urlCount} URLs)`);
  console.log(`   Robots.txt: ${report.robots.valid ? '✅' : '❌'}`);
  console.log(`   爬虫延迟: ${report.robots.hasCrawlDelay ? '✅' : '⚠️'}`);
  console.log(`   阻止有害爬虫: ${report.robots.blockedBots} 个`);
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 建议:');
    report.recommendations.forEach(rec => console.log(`   - ${rec}`));
  }
  
  return report;
}

// 主函数
function main() {
  console.log('🚀 开始SEO验证...\n');
  
  const sitemapValid = validateSitemap();
  const robotsValid = validateRobotsTxt();
  const report = generateSEOReport();
  
  console.log('\n' + '='.repeat(50));
  console.log('📈 SEO验证完成');
  console.log('='.repeat(50));
  
  if (sitemapValid && robotsValid) {
    console.log('🎉 所有SEO文件验证通过！');
    process.exit(0);
  } else {
    console.log('⚠️ 发现一些问题，请检查上述输出');
    process.exit(1);
  }
}

main();
