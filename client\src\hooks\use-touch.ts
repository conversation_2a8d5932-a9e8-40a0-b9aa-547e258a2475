import { useEffect, useRef, useState } from 'react';

interface TouchPosition {
  x: number;
  y: number;
}

interface SwipeDirection {
  horizontal: 'left' | 'right' | null;
  vertical: 'up' | 'down' | null;
}

interface UseTouchOptions {
  threshold?: number;
  onSwipe?: (direction: SwipeDirection) => void;
  onTap?: (position: TouchPosition) => void;
  onDoubleTap?: (position: TouchPosition) => void;
  onLongPress?: (position: TouchPosition) => void;
  longPressDelay?: number;
  doubleTapDelay?: number;
}

export function useTouch(
  ref: React.RefObject<HTMLElement>,
  {
    threshold = 50,
    onSwipe,
    onTap,
    onDoubleTap,
    onLongPress,
    longPressDelay = 500,
    doubleTapDelay = 300,
  }: UseTouchOptions = {}
) {
  const [isTouching, setIsTouching] = useState(false);
  const startPos = useRef<TouchPosition | null>(null);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const lastTapTime = useRef<number>(0);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length !== 1) return;
      
      setIsTouching(true);
      const touch = e.touches[0];
      startPos.current = { x: touch.clientX, y: touch.clientY };
      
      // 设置长按定时器
      if (onLongPress) {
        longPressTimer.current = setTimeout(() => {
          if (startPos.current) {
            onLongPress(startPos.current);
          }
        }, longPressDelay);
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      // 如果用户移动了手指，取消长按定时器
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      setIsTouching(false);
      
      // 清除长按定时器
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
      
      if (!startPos.current) return;
      
      const touch = e.changedTouches[0];
      const endPos = { x: touch.clientX, y: touch.clientY };
      const deltaX = endPos.x - startPos.current.x;
      const deltaY = endPos.y - startPos.current.y;
      
      // 检测是否为轻触
      const isTap = Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10;
      
      if (isTap) {
        // 处理点击
        if (onTap) {
          onTap(endPos);
        }
        
        // 处理双击
        if (onDoubleTap) {
          const now = Date.now();
          if (now - lastTapTime.current < doubleTapDelay) {
            onDoubleTap(endPos);
          }
          lastTapTime.current = now;
        }
      } else if (onSwipe) {
        // 处理滑动
        const direction: SwipeDirection = {
          horizontal: null,
          vertical: null
        };
        
        if (Math.abs(deltaX) > threshold) {
          direction.horizontal = deltaX > 0 ? 'right' : 'left';
        }
        
        if (Math.abs(deltaY) > threshold) {
          direction.vertical = deltaY > 0 ? 'down' : 'up';
        }
        
        if (direction.horizontal || direction.vertical) {
          onSwipe(direction);
        }
      }
      
      startPos.current = null;
    };

    const handleTouchCancel = () => {
      setIsTouching(false);
      startPos.current = null;
      
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: true });
    element.addEventListener('touchend', handleTouchEnd);
    element.addEventListener('touchcancel', handleTouchCancel);

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      element.removeEventListener('touchcancel', handleTouchCancel);
      
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, [ref, threshold, onSwipe, onTap, onDoubleTap, onLongPress, longPressDelay, doubleTapDelay]);

  return { isTouching };
}
