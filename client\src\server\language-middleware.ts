/**
 * 语言中间件 - 处理静态页面中的语言选择
 * 特别是针对禁用JavaScript的环境
 */

import { Request, Response, NextFunction } from 'express';

/**
 * 处理语言选择的中间件
 * 检测URL查询参数中的lang参数，并根据它重定向到相应的语言URL
 */
export function languageMiddleware(req: Request, res: Response, next: NextFunction) {
  // 检查是否有lang查询参数
  const langParam = req.query.lang as string | undefined;
  
  if (langParam && ['en', 'es', 'fr', 'de', 'zh'].includes(langParam)) {
    // 获取当前路径（不包括查询参数）
    const currentPath = req.path;
    const pathParts = currentPath.split('/').filter(Boolean);
    
    // 检查当前路径是否已经包含语言代码
    const hasLangCode = pathParts.length > 0 && ['en', 'es', 'fr', 'de', 'zh'].includes(pathParts[0]);
    
    // 构建新的URL路径
    let newPath: string;
    
    if (langParam === 'en') {
      // 英语是默认语言，如果是首页则不显示语言代码
      if (hasLangCode) {
        // 移除语言代码
        pathParts.shift();
        newPath = pathParts.length > 0 ? '/' + pathParts.join('/') + '/' : '/';
      } else {
        // 保持当前路径不变，但确保以/结尾
        newPath = currentPath.endsWith('/') ? currentPath : currentPath + '/';
      }
    } else {
      // 非英语语言，始终在URL中显示语言代码
      if (hasLangCode) {
        // 替换语言代码
        pathParts[0] = langParam;
      } else {
        // 添加语言代码
        pathParts.unshift(langParam);
      }
      newPath = '/' + pathParts.join('/') + '/';
    }
    
    // 确保URL不会有双斜杠
    newPath = newPath.replace(/\/\//g, '/');
    
    // 重定向到新路径（不带查询参数）
    return res.redirect(302, newPath);
  }
  
  // 没有语言参数，继续处理请求
  next();
}